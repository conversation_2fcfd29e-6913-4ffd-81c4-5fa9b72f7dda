{% extends 'base.html' %}
{% load static %}
{% load pharmacy_tags %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .pack-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .item-card {
        border-left: 4px solid #4e73df;
        transition: all 0.3s ease;
    }
    .item-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    .critical-item {
        border-left-color: #e74a3b;
        background-color: #fdf2f2;
    }
    .optional-item {
        border-left-color: #1cc88a;
        background-color: #f1f9f6;
    }
    .cost-display {
        font-size: 2rem;
        font-weight: 700;
        color: #1cc88a;
    }
    .badge-custom {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <div class="mb-3">
        <a href="{% url 'pharmacy:medical_pack_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Packs
        </a>
    </div>

    <!-- Pack Header -->
    <div class="pack-header text-center">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    {% if pack.pack_type == 'surgery' %}
                        <i class="fas fa-scalpel me-2"></i>
                    {% elif pack.pack_type == 'labor' %}
                        <i class="fas fa-baby me-2"></i>
                    {% elif pack.pack_type == 'emergency' %}
                        <i class="fas fa-ambulance me-2"></i>
                    {% else %}
                        <i class="fas fa-medical-kit me-2"></i>
                    {% endif %}
                    {{ pack.name }}
                </h1>
                <p class="mb-0 lead">{{ pack.description }}</p>
            </div>
            <div class="col-md-4">
                <div class="cost-display">₦{{ pack.get_total_cost|floatformat:2 }}</div>
                <small>Total Cost</small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Pack Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Pack Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Pack Type</label>
                        <div>
                            <span class="badge badge-custom
                                {% if pack.pack_type == 'surgery' %}bg-danger
                                {% elif pack.pack_type == 'labor' %}bg-pink
                                {% elif pack.pack_type == 'emergency' %}bg-warning text-dark
                                {% else %}bg-info{% endif %}">
                                {{ pack.get_pack_type_display }}
                            </span>
                        </div>
                    </div>

                    {% if pack.surgery_type %}
                    <div class="mb-3">
                        <label class="form-label text-muted">Surgery Type</label>
                        <div>
                            <span class="badge bg-secondary badge-custom">{{ pack.get_surgery_type_display }}</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if pack.labor_type %}
                    <div class="mb-3">
                        <label class="form-label text-muted">Labor Type</label>
                        <div>
                            <span class="badge bg-secondary badge-custom">{{ pack.get_labor_type_display }}</span>
                        </div>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label class="form-label text-muted">Risk Level</label>
                        <div>
                            <span class="badge badge-custom
                                {% if pack.risk_level == 'low' %}bg-success
                                {% elif pack.risk_level == 'medium' %}bg-warning text-dark
                                {% elif pack.risk_level == 'high' %}bg-danger
                                {% else %}bg-dark{% endif %}">
                                {{ pack.get_risk_level_display }}
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Total Items</label>
                        <div><strong>{{ pack.get_items_count }}</strong></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Total Quantity</label>
                        <div><strong>{{ pack.get_total_quantity }}</strong></div>
                    </div>

                    {% if pack.requires_approval %}
                    <div class="mb-3">
                        <span class="badge bg-warning text-dark badge-custom">
                            <i class="fas fa-exclamation-triangle"></i> Requires Approval
                        </span>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <div>
                            {% if pack.is_active %}
                                <span class="badge bg-success badge-custom">Active</span>
                            {% else %}
                                <span class="badge bg-secondary badge-custom">Inactive</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div><small>{{ pack.created_at|date:"M d, Y H:i" }}</small></div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if can_order %}
                            <a href="{% url 'pharmacy:create_pack_order_for_pack' pack.id %}" 
                               class="btn btn-success">
                                <i class="fas fa-shopping-cart"></i> Order This Pack
                            </a>
                        {% else %}
                            <button class="btn btn-warning" disabled title="{{ order_message }}">
                                <i class="fas fa-exclamation-triangle"></i> Cannot Order
                            </button>
                            <small class="text-muted">{{ order_message }}</small>
                        {% endif %}
                        
                        <a href="{% url 'pharmacy:edit_medical_pack' pack.id %}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Edit Pack
                        </a>
                        
                        <a href="{% url 'pharmacy:manage_pack_items' pack.id %}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> Manage Items
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pack Items -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Pack Contents</h6>
                    <a href="{% url 'pharmacy:manage_pack_items' pack.id %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus"></i> Add Item
                    </a>
                </div>
                <div class="card-body">
                    {% if pack.items.all %}
                        <div class="row">
                            {% for item in pack.items.all %}
                            <div class="col-md-6 mb-3">
                                <div class="card item-card h-100 
                                    {% if item.is_critical %}critical-item
                                    {% elif item.is_optional %}optional-item{% endif %}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">{{ item.medication.name }}</h6>
                                            <div class="text-end">
                                                {% if item.is_critical %}
                                                    <span class="badge bg-danger" title="Critical Item">
                                                        <i class="fas fa-exclamation"></i>
                                                    </span>
                                                {% elif item.is_optional %}
                                                    <span class="badge bg-success" title="Optional Item">
                                                        <i class="fas fa-question"></i>
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="row text-center mb-2">
                                            <div class="col-6">
                                                <div class="fw-bold text-primary">{{ item.quantity }}</div>
                                                <small class="text-muted">Quantity</small>
                                            </div>
                                            <div class="col-6">
                                                <div class="fw-bold text-success">₦{{ item.get_total_cost|floatformat:2 }}</div>
                                                <small class="text-muted">Cost</small>
                                            </div>
                                        </div>

                                        <div class="mb-2">
                                            <small class="text-muted">Type:</small>
                                            <span class="badge bg-light text-dark">{{ item.get_item_type_display }}</span>
                                        </div>

                                        {% if item.usage_instructions %}
                                        <div class="mb-2">
                                            <small class="text-muted">Instructions:</small>
                                            <p class="small mb-0">{{ item.usage_instructions|truncatewords:10 }}</p>
                                        </div>
                                        {% endif %}

                                        <div class="mt-2">
                                            <small class="text-muted">Unit Price: ₦{{ item.medication.price|floatformat:2 }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Items in Pack</h5>
                            <p class="text-muted">This pack doesn't have any items yet.</p>
                            <a href="{% url 'pharmacy:manage_pack_items' pack.id %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add First Item
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[title]').tooltip();
    
    // Add animations to cards
    $('.item-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
});
</script>
{% endblock %}