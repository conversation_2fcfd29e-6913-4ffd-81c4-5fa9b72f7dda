# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='OphthalmicRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('visual_acuity_right', models.CharField(blank=True, max_length=50, null=True)),
                ('visual_acuity_left', models.CharField(blank=True, max_length=50, null=True)),
                ('refraction_right_sphere', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('refraction_right_cylinder', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('refraction_right_axis', models.IntegerField(blank=True, null=True)),
                ('refraction_left_sphere', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('refraction_left_cylinder', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('refraction_left_axis', models.IntegerField(blank=True, null=True)),
                ('iop_right', models.DecimalField(blank=True, decimal_places=2, help_text='Intraocular Pressure (mmHg)', max_digits=5, null=True)),
                ('iop_left', models.DecimalField(blank=True, decimal_places=2, help_text='Intraocular Pressure (mmHg)', max_digits=5, null=True)),
                ('clinical_findings', models.TextField(blank=True, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ophthalmic_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Ophthalmic Record',
                'verbose_name_plural': 'Ophthalmic Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
