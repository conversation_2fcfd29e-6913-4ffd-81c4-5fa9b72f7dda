# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='IcuRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('gcs_score', models.IntegerField(blank=True, help_text='Glasgow Coma Scale Score', null=True)),
                ('respiratory_rate', models.IntegerField(blank=True, null=True)),
                ('oxygen_saturation', models.DecimalField(blank=True, decimal_places=2, help_text='Oxygen saturation in percentage', max_digits=5, null=True)),
                ('blood_pressure_systolic', models.IntegerField(blank=True, null=True)),
                ('blood_pressure_diastolic', models.IntegerField(blank=True, null=True)),
                ('heart_rate', models.IntegerField(blank=True, null=True)),
                ('body_temperature', models.DecimalField(blank=True, decimal_places=1, help_text='Body temperature in Celsius', max_digits=4, null=True)),
                ('mechanical_ventilation', models.BooleanField(default=False)),
                ('vasopressor_use', models.BooleanField(default=False)),
                ('dialysis_required', models.BooleanField(default=False)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='icu_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Icu Record',
                'verbose_name_plural': 'Icu Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
