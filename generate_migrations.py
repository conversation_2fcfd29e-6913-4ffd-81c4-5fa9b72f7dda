import os
from datetime import datetime

# Define the modules
modules = [
    'ophthalmic',
    'ent',
    'oncology',
    'scbu',
    'anc',
    'labor',
    'icu',
    'family_planning',
    'gynae_emergency'
]

# Define the base path
base_path = os.getcwd()

# Get current timestamp
timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")

# Generate migration files for each module
for module in modules:
    migrations_path = os.path.join(base_path, module, 'migrations')
    
    # Create migration file
    with open(os.path.join(migrations_path, '0001_initial.py'), 'w') as f:
        class_name = module.capitalize() if module != 'gynae_emergency' else 'GynaeEmergency'
        model_name = f"{class_name}Record" if module != 'gynae_emergency' else 'GynaeEmergencyRecord'
        
        f.write(f'''# Generated by Django 5.2 on {timestamp}

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='{model_name}',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('chief_complaint', models.TextField(blank=True, null=True)),
                ('history_of_present_illness', models.TextField(blank=True, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='{module}_records', to='patients.patient')),
            ],
            options={{
                'verbose_name': '{module.capitalize()} Record',
                'verbose_name_plural': '{module.capitalize()} Records',
                'ordering': ['-visit_date'],
            }},
        ),
    ]
''')

print("Migration files created successfully for all modules!")