{% extends 'base.html' %}

{% block title %}Patient Prescriptions - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Prescriptions for {{ patient.get_full_name }}</h4>
                <a href="{% url 'pharmacy:create_prescription' patient.id %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Create New Prescription
                </a>
            </div>
            <div class="card-body">
                {% if prescriptions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Prescription ID</th>
                                <th>Date</th>
                                <th>Doctor</th>
                                <th>Diagnosis</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prescription in prescriptions %}
                            <tr>
                                <td>{{ prescription.id }}</td>
                                <td>{{ prescription.prescription_date|date:"M d, Y" }}</td>
                                <td>Dr. {{ prescription.doctor.get_full_name }}</td>
                                <td>{{ prescription.diagnosis|truncatewords:5 }}</td>
                                <td>
                                    {% if prescription.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif prescription.status == 'approved' %}
                                        <span class="badge bg-info">Approved</span>
                                    {% elif prescription.status == 'dispensed' %}
                                        <span class="badge bg-success">Dispensed</span>
                                    {% elif prescription.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                    <h5>No prescriptions found</h5>
                    <p class="text-muted">This patient doesn't have any prescriptions yet.</p>
                    <a href="{% url 'pharmacy:create_prescription' patient.id %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Prescription
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}