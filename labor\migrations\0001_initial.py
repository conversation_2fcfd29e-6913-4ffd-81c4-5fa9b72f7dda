# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='LaborRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('onset_time', models.DateTimeField(blank=True, null=True)),
                ('presentation', models.CharField(blank=True, max_length=50, null=True)),
                ('fetal_heart_rate', models.IntegerField(blank=True, null=True)),
                ('cervical_dilation', models.IntegerField(blank=True, help_text='Cervical dilation in cm', null=True)),
                ('effacement', models.IntegerField(blank=True, help_text='Effacement in percentage', null=True)),
                ('rupture_of_membranes', models.BooleanField(default=False)),
                ('rupture_time', models.DateTimeField(blank=True, null=True)),
                ('mode_of_delivery', models.CharField(blank=True, max_length=50, null=True)),
                ('duration_first_stage', models.DurationField(blank=True, null=True)),
                ('placenta_delivery_time', models.DateTimeField(blank=True, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='labor_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Labor Record',
                'verbose_name_plural': 'Labor Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
