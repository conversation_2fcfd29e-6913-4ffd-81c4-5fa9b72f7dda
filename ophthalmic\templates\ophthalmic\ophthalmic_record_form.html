{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        
                <div class="form-group">
                    <label for="{{ form.visual_acuity_right.id_for_label }}">Visual Acuity Right</label>
                    {{ form.visual_acuity_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.visual_acuity_left.id_for_label }}">Visual Acuity Left</label>
                    {{ form.visual_acuity_left|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_right_sphere.id_for_label }}">Refraction Right Sphere</label>
                    {{ form.refraction_right_sphere|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_right_cylinder.id_for_label }}">Refraction Right Cylinder</label>
                    {{ form.refraction_right_cylinder|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_right_axis.id_for_label }}">Refraction Right Axis</label>
                    {{ form.refraction_right_axis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_left_sphere.id_for_label }}">Refraction Left Sphere</label>
                    {{ form.refraction_left_sphere|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_left_cylinder.id_for_label }}">Refraction Left Cylinder</label>
                    {{ form.refraction_left_cylinder|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.refraction_left_axis.id_for_label }}">Refraction Left Axis</label>
                    {{ form.refraction_left_axis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.iop_right.id_for_label }}">IOP Right (mmHg)</label>
                    {{ form.iop_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.iop_left.id_for_label }}">IOP Left (mmHg)</label>
                    {{ form.iop_left|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.clinical_findings.id_for_label }}">Clinical Findings</label>
                    {{ form.clinical_findings|add_class:"form-control" }}
                </div>
                
<div class="form-group">
                            <label for="{{ form.patient.id_for_label }}">Patient *</label>
                            {{ form.patient|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.doctor.id_for_label }}">Doctor</label>
                            {{ form.doctor|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.visit_date.id_for_label }}">Visit Date *</label>
                            {{ form.visit_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.chief_complaint.id_for_label }}">Chief Complaint</label>
                    {{ form.chief_complaint|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.history_of_present_illness.id_for_label }}">History of Present Illness</label>
                    {{ form.history_of_present_illness|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.diagnosis.id_for_label }}">Diagnosis</label>
                    {{ form.diagnosis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.treatment_plan.id_for_label }}">Treatment Plan</label>
                    {{ form.treatment_plan|add_class:"form-control" }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_required.id_for_label }}">Follow-up Required</label>
                            {{ form.follow_up_required|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_date.id_for_label }}">Follow-up Date</label>
                            {{ form.follow_up_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.authorization_code.id_for_label }}">Authorization Code (Optional)</label>
                    {{ form.authorization_code|add_class:"form-control" }}
                    <small class="form-text text-muted">Enter authorization code from desk office if applicable</small>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}">Notes</label>
                    {{ form.notes|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Record
                </button>
                <a href="{% url 'ophthalmic:ophthalmic_records_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}