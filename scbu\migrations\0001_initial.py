# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScbuRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('gestational_age', models.DecimalField(blank=True, decimal_places=1, help_text='Gestational age in weeks', max_digits=3, null=True)),
                ('birth_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Birth weight in kg', max_digits=4, null=True)),
                ('apgar_score_1min', models.IntegerField(blank=True, help_text='APGAR score at 1 minute', null=True)),
                ('apgar_score_5min', models.IntegerField(blank=True, help_text='APGAR score at 5 minutes', null=True)),
                ('respiratory_support', models.BooleanField(default=False)),
                ('ventilation_type', models.CharField(blank=True, max_length=50, null=True)),
                ('feeding_method', models.CharField(blank=True, max_length=50, null=True)),
                ('infection_status', models.BooleanField(default=False)),
                ('antibiotic_name', models.CharField(blank=True, max_length=100, null=True)),
                ('discharge_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Discharge weight in kg', max_digits=4, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scbu_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Scbu Record',
                'verbose_name_plural': 'Scbu Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
