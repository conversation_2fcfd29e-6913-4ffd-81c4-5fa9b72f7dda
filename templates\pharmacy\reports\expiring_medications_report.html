{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <!-- Expired Medications Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">Expired Medications</h6>
        </div>
        <div class="card-body">
            {% if expired %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Dispensary</th>
                            <th>Batch Number</th>
                            <th>Expiry Date</th>
                            <th>Days Expired</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in expired %}
                        <tr>
                            <td>{{ item.medication.name }} ({{ item.medication.strength }})</td>
                            <td>{{ item.active_store.dispensary.name }}</td>
                            <td>{{ item.batch_number|default:"N/A" }}</td>
                            <td>{{ item.expiry_date }}</td>
                            <td>{{ item.expiry_date|timesince }}</td>
                            <td>{{ item.stock_quantity }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No expired medications found.</p>
            {% endif %}
        </div>
    </div>

    <!-- Expiring Soon Medications Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Expiring Soon (Within 30 Days)</h6>
        </div>
        <div class="card-body">
            {% if expiring_soon %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Dispensary</th>
                            <th>Batch Number</th>
                            <th>Expiry Date</th>
                            <th>Days Until Expiry</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in expiring_soon %}
                        <tr>
                            <td>{{ item.medication.name }} ({{ item.medication.strength }})</td>
                            <td>{{ item.active_store.dispensary.name }}</td>
                            <td>{{ item.batch_number|default:"N/A" }}</td>
                            <td>{{ item.expiry_date }}</td>
                            <td>{{ item.expiry_date|timeuntil }}</td>
                            <td>{{ item.stock_quantity }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No medications expiring within 30 days.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}