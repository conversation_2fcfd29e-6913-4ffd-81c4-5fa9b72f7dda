{% extends 'base.html' %}
{% load static %}

{% block title %}Consultation Orders - {{ consultation.patient.get_full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-file-medical"></i> Orders for Consultation
            <small class="text-muted">#{{ consultation.id }}</small>
        </h2>
        <a href="{% url 'consultations:consultation_detail' consultation.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Consultation
        </a>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-injured"></i> Patient Information
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>Name:</strong> {{ consultation.patient.get_full_name }}</p>
                    <p class="mb-1"><strong>ID:</strong> {{ consultation.patient.patient_id }}</p>
                    <p class="mb-1"><strong>Age:</strong> {{ consultation.patient.age }} years</p>
                    <p class="mb-0"><strong>Gender:</strong> {{ consultation.patient.get_gender_display }}</p>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-stethoscope"></i> Consultation Details
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>Date:</strong> {{ consultation.consultation_date|date:"M d, Y H:i" }}</p>
                    <p class="mb-1"><strong>Doctor:</strong> 
                        {% if consultation.doctor %}
                            Dr. {{ consultation.doctor.get_full_name }}
                        {% else %}
                            Not assigned
                        {% endif %}
                    </p>
                    <p class="mb-1"><strong>Status:</strong> 
                        <span class="badge bg-{{ consultation.get_status_display|lower }}">
                            {{ consultation.get_status_display }}
                        </span>
                    </p>
                    <p class="mb-0"><strong>Chief Complaint:</strong> {{ consultation.chief_complaint }}</p>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Orders
                    </h5>
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createOrderModal">
                        <i class="fas fa-plus"></i> Create New Order
                    </button>
                </div>
                <div class="card-body">
                    {% if orders %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Order Type</th>
                                        <th>Details</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders %}
                                    <tr>
                                        <td>
                                            {% if order.order_type == 'lab_test' %}
                                                <i class="fas fa-vial text-primary"></i> Laboratory
                                            {% elif order.order_type == 'radiology' %}
                                                <i class="fas fa-x-ray text-info"></i> Radiology
                                            {% elif order.order_type == 'prescription' %}
                                                <i class="fas fa-prescription text-success"></i> Prescription
                                            {% endif %}
                                        </td>
                                        <td>{{ order.get_order_details }}</td>
                                        <td>
                                            <span class="badge bg-{{ order.get_status_display|lower }}">
                                                {{ order.get_status_display }}
                                            </span>
                                        </td>
                                        <td>{{ order.created_at|date:"M d, Y H:i" }}</td>
                                        <td>
                                            {% if order.order_type == 'lab_test' %}
                                                <a href="{% url 'laboratory:test_request_detail' order.order_object.id %}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            {% elif order.order_type == 'radiology' %}
                                                <a href="{% url 'radiology:order_detail' order.order_object.id %}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            {% elif order.order_type == 'prescription' %}
                                                <a href="{% url 'pharmacy:prescription_detail' order.order_object.id %}" 
                                                   class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-medical fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No orders created yet</h5>
                            <p class="text-muted">Create your first order using the button above</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Order Modal -->
<div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createOrderModalLabel">
                    <i class="fas fa-plus-circle"></i> Create New Order
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="orderTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="lab-tab" data-bs-toggle="tab" data-bs-target="#lab" type="button" role="tab">
                            <i class="fas fa-vial"></i> Laboratory
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="radiology-tab" data-bs-toggle="tab" data-bs-target="#radiology" type="button" role="tab">
                            <i class="fas fa-x-ray"></i> Radiology
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="prescription-tab" data-bs-toggle="tab" data-bs-target="#prescription" type="button" role="tab">
                            <i class="fas fa-prescription"></i> Prescription
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content mt-3" id="orderTabsContent">
                    <!-- Laboratory Tab -->
                    <div class="tab-pane fade show active" id="lab" role="tabpanel">
                        <form id="labOrderForm" method="post" action="{% url 'consultations:create_lab_order_ajax' consultation.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">Laboratory Tests</label>
                                {{ lab_order_form.tests }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                {{ lab_order_form.priority }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                {{ lab_order_form.notes }}
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Lab Order
                            </button>
                        </form>
                    </div>
                    
                    <!-- Radiology Tab -->
                    <div class="tab-pane fade" id="radiology" role="tabpanel">
                        <form id="radiologyOrderForm" method="post" action="{% url 'consultations:create_radiology_order_ajax' consultation.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">Radiology Test</label>
                                {{ radiology_order_form.test }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                {{ radiology_order_form.priority }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Clinical Information</label>
                                {{ radiology_order_form.clinical_information }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                {{ radiology_order_form.notes }}
                            </div>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-plus"></i> Create Radiology Order
                            </button>
                        </form>
                    </div>
                    
                    <!-- Prescription Tab -->
                    <div class="tab-pane fade" id="prescription" role="tabpanel">
                        <form id="prescriptionForm" method="post" action="{% url 'consultations:create_prescription_ajax' consultation.id %}">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label class="form-label">Diagnosis</label>
                                {{ prescription_form.diagnosis }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Prescription Type</label>
                                {{ prescription_form.prescription_type }}
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                {{ prescription_form.notes }}
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-plus"></i> Create Prescription
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for handling form submissions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle lab order form submission
    document.getElementById('labOrderForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const form = this;
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert(data.message);
                // Reload the page to show the new order
                location.reload();
            } else {
                // Show error message
                if (data.errors) {
                    alert('Please correct the errors in the form.');
                } else {
                    alert('Error: ' + data.error);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the order.');
        });
    });
    
    // Handle radiology order form submission
    document.getElementById('radiologyOrderForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const form = this;
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert(data.message);
                // Reload the page to show the new order
                location.reload();
            } else {
                // Show error message
                if (data.errors) {
                    alert('Please correct the errors in the form.');
                } else {
                    alert('Error: ' + data.error);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the order.');
        });
    });
    
    // Handle prescription form submission
    document.getElementById('prescriptionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const form = this;
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                alert(data.message);
                // Reload the page to show the new order
                location.reload();
            } else {
                // Show error message
                if (data.errors) {
                    alert('Please correct the errors in the form.');
                } else {
                    alert('Error: ' + data.error);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the order.');
        });
    });
});
</script>
{% endblock %}