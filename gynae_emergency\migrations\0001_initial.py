# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='Gynae_emergencyRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('emergency_type', models.CharField(blank=True, max_length=100, null=True)),
                ('pain_level', models.IntegerField(blank=True, help_text='Pain level on scale of 1-10', null=True)),
                ('bleeding_amount', models.CharField(blank=True, max_length=50, null=True)),
                ('contractions', models.BooleanField(default=False)),
                ('contraction_frequency', models.CharField(blank=True, max_length=50, null=True)),
                ('rupture_of_membranes', models.BooleanField(default=False)),
                ('fetal_movement', models.CharField(blank=True, max_length=50, null=True)),
                ('vaginal_discharge', models.TextField(blank=True, null=True)),
                ('emergency_intervention', models.TextField(blank=True, null=True)),
                ('stabilization_status', models.CharField(blank=True, max_length=50, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gynae_emergency_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Gynae_emergency Record',
                'verbose_name_plural': 'Gynae_emergency Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
