# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='AncR<PERSON>ord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('gravida', models.IntegerField(blank=True, null=True)),
                ('para', models.IntegerField(blank=True, null=True)),
                ('abortions', models.IntegerField(blank=True, null=True)),
                ('lmp', models.DateField(blank=True, help_text='Last Menstrual Period', null=True)),
                ('edd', models.DateField(blank=True, help_text='Expected Date of Delivery', null=True)),
                ('fundal_height', models.DecimalField(blank=True, decimal_places=1, help_text='Fundal height in cm', max_digits=4, null=True)),
                ('fetal_heartbeat', models.BooleanField(default=False)),
                ('fetal_position', models.CharField(blank=True, max_length=50, null=True)),
                ('blood_pressure', models.CharField(blank=True, max_length=20, null=True)),
                ('urine_protein', models.CharField(blank=True, max_length=20, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='anc_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Anc Record',
                'verbose_name_plural': 'Anc Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
