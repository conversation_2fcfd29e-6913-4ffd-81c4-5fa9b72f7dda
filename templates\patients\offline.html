{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 text-center">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-wifi-slash"></i> You're Offline
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h4 class="text-gray-800 mb-3">No Internet Connection</h4>
                    
                    <p class="text-gray-600 mb-4">
                        {{ message }}
                    </p>
                    
                    <div class="mb-4">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Try Again
                        </button>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> What you can do:</h6>
                        <ul class="text-left mb-0">
                            <li>Check your internet connection</li>
                            <li>Try refreshing the page</li>
                            <li>Contact your network administrator if the problem persists</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Check for online status and auto-refresh when back online
window.addEventListener('online', function() {
    location.reload();
});

// Show connection status
function updateConnectionStatus() {
    if (navigator.onLine) {
        location.reload();
    }
}

// Check connection every 5 seconds
setInterval(updateConnectionStatus, 5000);
</script>
{% endblock %}
