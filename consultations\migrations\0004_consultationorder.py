# Generated by Django 5.2 on 2025-08-19 15:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('consultations', '0003_alter_consultation_doctor'),
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsultationOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_type', models.CharField(choices=[('lab_test', 'Laboratory Test'), ('radiology', 'Radiology Order'), ('prescription', 'Prescription')], max_length=20)),
                ('object_id', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('ordered', 'Ordered'), ('processing', 'Processing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='ordered', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('consultation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='consultations.consultation')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_consultation_orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['consultation'], name='consultatio_consult_8e6173_idx'), models.Index(fields=['order_type'], name='consultatio_order_t_eba7fb_idx'), models.Index(fields=['status'], name='consultatio_status_60d802_idx')],
            },
        ),
    ]
