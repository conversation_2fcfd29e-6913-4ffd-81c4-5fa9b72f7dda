{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="form-group">
                    <label for="{{ form.test_type.id_for_label }}">Test Type *</label>
                    {{ form.test_type|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.test_name.id_for_label }}">Test Name *</label>
                    {{ form.test_name|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.test_date.id_for_label }}">Test Date *</label>
                    {{ form.test_date|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.test_results.id_for_label }}">Test Results *</label>
                    {{ form.test_results|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}">Notes</label>
                    {{ form.notes|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Test
                </button>
                <a href="{% url 'ophthalmic:ophthalmic_record_detail' record.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}