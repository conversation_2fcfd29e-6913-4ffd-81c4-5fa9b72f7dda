{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        
                <div class="form-group">
                    <label for="{{ form.external_ear_right.id_for_label }}">External Ear Right</label>
                    {{ form.external_ear_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.external_ear_left.id_for_label }}">External Ear Left</label>
                    {{ form.external_ear_left|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.ear_canal_right.id_for_label }}">Ear Canal Right</label>
                    {{ form.ear_canal_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.ear_canal_left.id_for_label }}">Ear Canal Left</label>
                    {{ form.ear_canal_left|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.tympanic_membrane_right.id_for_label }}">Tympanic Membrane Right</label>
                    {{ form.tympanic_membrane_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.tympanic_membrane_left.id_for_label }}">Tympanic Membrane Left</label>
                    {{ form.tympanic_membrane_left|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.nose_examination.id_for_label }}">Nose Examination</label>
                    {{ form.nose_examination|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.throat_examination.id_for_label }}">Throat Examination</label>
                    {{ form.throat_examination|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.neck_examination.id_for_label }}">Neck Examination</label>
                    {{ form.neck_examination|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.audio_test_right.id_for_label }}">Audio Test Right</label>
                    {{ form.audio_test_right|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.audio_test_left.id_for_label }}">Audio Test Left</label>
                    {{ form.audio_test_left|add_class:"form-control" }}
                </div>
                
<div class="form-group">
                            <label for="{{ form.patient.id_for_label }}">Patient *</label>
                            {{ form.patient|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.doctor.id_for_label }}">Doctor</label>
                            {{ form.doctor|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.visit_date.id_for_label }}">Visit Date *</label>
                            {{ form.visit_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.chief_complaint.id_for_label }}">Chief Complaint</label>
                    {{ form.chief_complaint|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.history_of_present_illness.id_for_label }}">History of Present Illness</label>
                    {{ form.history_of_present_illness|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.diagnosis.id_for_label }}">Diagnosis</label>
                    {{ form.diagnosis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.treatment_plan.id_for_label }}">Treatment Plan</label>
                    {{ form.treatment_plan|add_class:"form-control" }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_required.id_for_label }}">Follow-up Required</label>
                            {{ form.follow_up_required|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_date.id_for_label }}">Follow-up Date</label>
                            {{ form.follow_up_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}">Notes</label>
                    {{ form.notes|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Record
                </button>
                <a href="{% url 'ent:ent_records_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}