# Generated by Django 5.2 on 2025-08-23 18:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('labor', '0002_laborrecord_authorization_code'),
        ('patients', '0006_add_new_transaction_types'),
        ('pharmacy', '0008_alter_medicationinventory_options_and_more'),
        ('theatre', '0006_surgery_authorization_code'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalPack',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('pack_type', models.CharField(choices=[('surgery', 'Surgery Pack'), ('labor', 'Labor/Delivery Pack'), ('emergency', 'Emergency Pack'), ('general', 'General Medical Pack')], max_length=20)),
                ('surgery_type', models.CharField(blank=True, choices=[('appendectomy', 'Appendectomy'), ('cholecystectomy', 'Cholecystectomy'), ('hernia_repair', 'Hernia Repair'), ('cesarean_section', 'Cesarean Section'), ('tonsillectomy', 'Tonsillectomy'), ('orthopedic_surgery', 'Orthopedic Surgery'), ('cardiac_surgery', 'Cardiac Surgery'), ('neurosurgery', 'Neurosurgery'), ('general_surgery', 'General Surgery'), ('plastic_surgery', 'Plastic Surgery')], max_length=50, null=True)),
                ('labor_type', models.CharField(blank=True, choices=[('normal_delivery', 'Normal Delivery'), ('assisted_delivery', 'Assisted Delivery'), ('cesarean_delivery', 'Cesarean Delivery'), ('labor_induction', 'Labor Induction'), ('episiotomy', 'Episiotomy'), ('emergency_delivery', 'Emergency Delivery')], max_length=50, null=True)),
                ('risk_level', models.CharField(choices=[('low', 'Low Risk'), ('medium', 'Medium Risk'), ('high', 'High Risk'), ('critical', 'Critical Risk')], default='medium', max_length=20)),
                ('total_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('requires_approval', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Medical Pack',
                'verbose_name_plural': 'Medical Packs',
                'ordering': ['pack_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PackItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField()),
                ('item_type', models.CharField(choices=[('medication', 'Medication'), ('consumable', 'Consumable'), ('equipment', 'Equipment'), ('supply', 'Medical Supply')], default='medication', max_length=20)),
                ('usage_instructions', models.TextField(blank=True, null=True)),
                ('is_critical', models.BooleanField(default=False, help_text='Critical item that cannot be substituted')),
                ('is_optional', models.BooleanField(default=False, help_text='Optional item that can be omitted if not available')),
                ('order', models.PositiveIntegerField(default=0, help_text='Order of usage in procedure')),
                ('medication', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='pharmacy.medication')),
                ('pack', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='pharmacy.medicalpack')),
            ],
            options={
                'verbose_name': 'Pack Item',
                'verbose_name_plural': 'Pack Items',
                'ordering': ['order', 'medication__name'],
            },
        ),
        migrations.CreateModel(
            name='PackOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_date', models.DateTimeField(auto_now_add=True)),
                ('scheduled_date', models.DateTimeField(blank=True, help_text='When the pack is needed', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('processing', 'Processing'), ('ready', 'Ready for Collection'), ('dispensed', 'Dispensed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('dispensed_at', models.DateTimeField(blank=True, null=True)),
                ('order_notes', models.TextField(blank=True, null=True)),
                ('processing_notes', models.TextField(blank=True, null=True)),
                ('dispensed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dispensed_packs', to=settings.AUTH_USER_MODEL)),
                ('labor_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pack_orders', to='labor.laborrecord')),
                ('ordered_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ordered_packs', to=settings.AUTH_USER_MODEL)),
                ('pack', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='pharmacy.medicalpack')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pack_orders', to='patients.patient')),
                ('prescription', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pack_orders', to='pharmacy.prescription')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_packs', to=settings.AUTH_USER_MODEL)),
                ('surgery', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pack_orders', to='theatre.surgery')),
            ],
            options={
                'verbose_name': 'Pack Order',
                'verbose_name_plural': 'Pack Orders',
                'ordering': ['-order_date'],
            },
        ),
        migrations.AddIndex(
            model_name='medicalpack',
            index=models.Index(fields=['pack_type'], name='pharmacy_me_pack_ty_fb364d_idx'),
        ),
        migrations.AddIndex(
            model_name='medicalpack',
            index=models.Index(fields=['surgery_type'], name='pharmacy_me_surgery_9a2880_idx'),
        ),
        migrations.AddIndex(
            model_name='medicalpack',
            index=models.Index(fields=['labor_type'], name='pharmacy_me_labor_t_320687_idx'),
        ),
        migrations.AddIndex(
            model_name='medicalpack',
            index=models.Index(fields=['is_active'], name='pharmacy_me_is_acti_5b9413_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='packitem',
            unique_together={('pack', 'medication')},
        ),
        migrations.AddIndex(
            model_name='packorder',
            index=models.Index(fields=['status'], name='pharmacy_pa_status_5871ef_idx'),
        ),
        migrations.AddIndex(
            model_name='packorder',
            index=models.Index(fields=['order_date'], name='pharmacy_pa_order_d_004011_idx'),
        ),
        migrations.AddIndex(
            model_name='packorder',
            index=models.Index(fields=['scheduled_date'], name='pharmacy_pa_schedul_daf2b5_idx'),
        ),
    ]
