{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .process-header {
        background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .pack-info-card {
        background: #f8f9fc;
        border-left: 4px solid #007bff;
        padding: 1.5rem;
        border-radius: 0.35rem;
        margin-bottom: 1.5rem;
    }
    
    .patient-info-card {
        background: #e8f4f8;
        border-left: 4px solid #17a2b8;
        padding: 1.5rem;
        border-radius: 0.35rem;
        margin-bottom: 1.5rem;
    }
    
    .process-actions {
        background: #fff;
        border: 2px dashed #007bff;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
    }
    
    .btn-process {
        background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
        border: none;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn-process:hover {
        background: linear-gradient(135deg, #0056b3 0%, #5a32a1 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    @media (max-width: 768px) {
        .info-grid {
            grid-template-columns: 1fr;
        }
    }
    
    .process-warning {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Back Button -->
    <div class="mb-3">
        <a href="{% url 'pharmacy:pack_order_detail' pack_order.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Order Details
        </a>
    </div>

    <!-- Process Header -->
    <div class="process-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-cogs me-2"></i>
                    Process Pack Order
                </h1>
                <p class="mb-0 h5">Order #{{ pack_order.id }} - {{ pack_order.pack.name }}</p>
            </div>
            <div class="col-md-4 text-center">
                <div class="process-warning">
                    <i class="fas fa-cogs fa-3x mb-2"></i>
                    <div class="h6">Ready for Processing</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8">
            <!-- Pack Information -->
            <div class="pack-info-card">
                <h5 class="text-primary mb-3">
                    <i class="fas fa-box"></i> Pack Information
                </h5>
                <div class="info-grid">
                    <div>
                        <strong>Pack Name:</strong><br>
                        {{ pack_order.pack.name }}
                    </div>
                    <div>
                        <strong>Pack Type:</strong><br>
                        {{ pack_order.pack.get_pack_type_display }}
                    </div>
                    <div>
                        <strong>Total Items:</strong><br>
                        {{ pack_order.pack.items.count }} medication(s)
                    </div>
                    <div>
                        <strong>Total Cost:</strong><br>
                        <span class="h6 text-success">₦{{ pack_order.pack.get_total_cost|floatformat:2 }}</span>
                    </div>
                </div>
                
                {% if pack_order.pack.description %}
                <div class="mt-3">
                    <strong>Description:</strong><br>
                    {{ pack_order.pack.description }}
                </div>
                {% endif %}
            </div>

            <!-- Patient Information -->
            <div class="patient-info-card">
                <h5 class="text-info mb-3">
                    <i class="fas fa-user-injured"></i> Patient Information
                </h5>
                <div class="info-grid">
                    <div>
                        <strong>Patient Name:</strong><br>
                        {{ pack_order.patient.get_full_name }}
                    </div>
                    <div>
                        <strong>Patient ID:</strong><br>
                        {{ pack_order.patient.patient_id }}
                    </div>
                    <div>
                        <strong>Gender:</strong><br>
                        {{ pack_order.patient.get_gender_display }}
                    </div>
                    <div>
                        <strong>Phone:</strong><br>
                        {{ pack_order.patient.phone_number|default:"Not provided" }}
                    </div>
                </div>
            </div>

            <!-- Order Details -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> Order Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="info-grid">
                        <div>
                            <strong>Order Date:</strong><br>
                            {{ pack_order.order_date|date:"M d, Y H:i" }}
                        </div>
                        <div>
                            <strong>Ordered By:</strong><br>
                            {{ pack_order.ordered_by.get_full_name|default:"Unknown" }}
                        </div>
                        {% if pack_order.scheduled_date %}
                        <div>
                            <strong>Scheduled Date:</strong><br>
                            {{ pack_order.scheduled_date|date:"M d, Y H:i" }}
                        </div>
                        {% endif %}
                        <div>
                            <strong>Current Status:</strong><br>
                            {% if pack_order.status == 'approved' %}
                                <span class="badge badge-info">
                                    <i class="fas fa-check"></i> {{ pack_order.get_status_display }}
                                </span>
                            {% else %}
                                <span class="badge badge-secondary">
                                    {{ pack_order.get_status_display }}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if pack_order.approved_at %}
                    <div class="mt-3">
                        <strong>Approved:</strong><br>
                        <span class="text-success">{{ pack_order.approved_at|date:"M d, Y H:i" }}</span>
                    </div>
                    {% endif %}
                    
                    {% if pack_order.order_notes %}
                    <div class="mt-3">
                        <strong>Order Notes:</strong><br>
                        <div class="alert alert-secondary">
                            {{ pack_order.order_notes }}
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if pack_order.processing_notes %}
                    <div class="mt-3">
                        <strong>Processing Notes:</strong><br>
                        <div class="alert alert-info">
                            {{ pack_order.processing_notes|linebreaksbr }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Pack Items -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-pills"></i> Pack Contents
                    </h6>
                </div>
                <div class="card-body">
                    {% if pack_order.pack.items.all %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Medication</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total Price</th>
                                        <th>Instructions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in pack_order.pack.items.all %}
                                    <tr>
                                        <td>
                                            <strong>{{ item.medication.name }}</strong>
                                            {% if item.is_critical %}
                                                <span class="badge badge-danger badge-sm ml-1">Critical</span>
                                            {% elif item.is_optional %}
                                                <span class="badge badge-secondary badge-sm ml-1">Optional</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ item.quantity }}</td>
                                        <td>₦{{ item.medication.price|floatformat:2 }}</td>
                                        <td>₦{{ item.get_total_cost|floatformat:2 }}</td>
                                        <td>{{ item.usage_instructions|default:"As needed" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No items in this pack.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Process Actions -->
        <div class="col-lg-4">
            <div class="process-actions">
                <h5 class="text-primary mb-4">
                    <i class="fas fa-cogs"></i> Process Order
                </h5>
                
                <p class="text-muted mb-4">
                    Processing this pack order will convert it into individual prescription items and create a prescription for the patient.
                </p>
                
                <form method="post" id="process-form">
                    {% csrf_token %}
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-process">
                            <i class="fas fa-cogs"></i> Process Pack Order
                        </button>
                        <a href="{% url 'pharmacy:pack_order_detail' pack_order.id %}" 
                           class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>

            <!-- Process Information -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle"></i> Processing Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-lightbulb"></i> What Happens Next?
                        </h6>
                        <p class="mb-2">When you process this pack order:</p>
                        <ul class="mb-0">
                            <li>A new prescription will be automatically created</li>
                            <li>Each pack item becomes a prescription item</li>
                            <li>The order status changes to "Ready"</li>
                            <li>The pack becomes available for dispensing</li>
                        </ul>
                    </div>
                    
                    <div class="text-sm text-muted">
                        <strong>After Processing:</strong><br>
                        You'll be redirected to the new prescription where you can manage individual items and proceed with dispensing.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('process-form');
    
    form.addEventListener('submit', function(e) {
        const confirmMessage = `Are you sure you want to process Pack Order #{{ pack_order.id }}?\n\n` +
                              `This will create a new prescription with {{ pack_order.pack.items.count }} medication items.\n\n` +
                              `Pack: {{ pack_order.pack.name }}\n` +
                              `Patient: {{ pack_order.patient.get_full_name }}\n\n` +
                              `This action cannot be undone.`;
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}