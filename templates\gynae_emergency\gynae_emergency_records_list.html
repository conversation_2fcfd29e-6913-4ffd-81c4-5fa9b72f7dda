{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Gynae Emergency Records - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Gynae Emergency Records</h4>
                <a href="{% url 'gynae_emergency:create_gynae_emergency_record' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Add Gynae Emergency Record
                </a>
            </div>
            <div class="card-body">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Records</h5>
                                <h2 class="mb-0">{{ total_records }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">With Follow-up</h5>
                                <h2 class="mb-0">{{ follow_up_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Form -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="{{ search_form.search.id_for_label }}" class="form-label">Search</label>
                                {{ search_form.search }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.date_from.id_for_label }}" class="form-label">From Date</label>
                                {{ search_form.date_from }}
                            </div>
                            <div class="col-md-3">
                                <label for="{{ search_form.date_to.id_for_label }}" class="form-label">To Date</label>
                                {{ search_form.date_to }}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <div>
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                    <a href="{% url 'gynae_emergency:gynae_emergency_records_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-redo"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Gynae Emergency Records Table -->
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Patient</th>
                                <th>Doctor</th>
                                <th>Visit Date</th>
                                <th>Diagnosis</th>
                                <th>Follow-up</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in page_obj %}
                            <tr>
                                <td>{{ record.id }}</td>
                                <td>
                                    <a href="{% url 'gynae_emergency:gynae_emergency_record_detail' record.id %}">
                                        {{ record.patient.get_full_name }}
                                    </a>
                                </td>
                                <td>
                                    {% if record.doctor %}
                                        Dr. {{ record.doctor.get_full_name }}
                                    {% else %}
                                        Not assigned
                                    {% endif %}
                                </td>
                                <td>{{ record.visit_date|date:"M d, Y" }}</td>
                                <td>{{ record.diagnosis|truncatewords:5 }}</td>
                                <td>
                                    {% if record.follow_up_required %}
                                        <span class="badge bg-warning">Yes</span>
                                        {% if record.follow_up_date %}
                                            <br><small>{{ record.follow_up_date|date:"M d, Y" }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-success">No</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'gynae_emergency:gynae_emergency_record_detail' record.id %}" class="btn btn-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'gynae_emergency:edit_gynae_emergency_record' record.id %}" class="btn btn-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'gynae_emergency:create_prescription_for_gynae_emergency' record.id %}" class="btn btn-success" title="Prescribe">
                                            <i class="fas fa-prescription"></i>
                                        </a>
                                        <a href="{% url 'gynae_emergency:delete_gynae_emergency_record' record.id %}" class="btn btn-danger" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">No gynae emergency records found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No gynae emergency records available.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}