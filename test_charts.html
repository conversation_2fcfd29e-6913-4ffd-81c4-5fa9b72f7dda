<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revenue Charts Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 Hospital Revenue Analysis - Charts Test</h1>
        <p>This page tests the Chart.js integration with sample data from the HMS revenue system.</p>
        
        <!-- Revenue Stats -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value">₦537,325.00</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₦525,305.00</div>
                <div class="stat-label">Wallet Transactions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₦2,920.00</div>
                <div class="stat-label">Pharmacy Revenue</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">₦7,100.00</div>
                <div class="stat-label">General Services</div>
            </div>
        </div>

        <!-- Monthly Trends Chart -->
        <h2>📈 Monthly Revenue Trends</h2>
        <div class="chart-container">
            <canvas id="trendsChart"></canvas>
        </div>

        <!-- Revenue Distribution Chart -->
        <h2>🥧 Revenue Distribution</h2>
        <div class="chart-container">
            <canvas id="distributionChart"></canvas>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <h3>✅ Test Results:</h3>
            <p>If you can see the charts above with data, then the Chart.js integration is working correctly.</p>
            <p>The actual HMS revenue analysis page should display similar charts with real data.</p>
            <p><strong>Next Steps:</strong> Visit <a href="http://127.0.0.1:8000/pharmacy/revenue/comprehensive/" target="_blank">http://127.0.0.1:8000/pharmacy/revenue/comprehensive/</a> (login may be required)</p>
        </div>
    </div>

    <script>
        // Sample data matching what we got from the test script
        const monthlyData = {
            months: ["Feb 2025", "Mar 2025", "Apr 2025", "May 2025", "Jun 2025", "Jul 2025", "Aug 2025"],
            pharmacy: [0.0, 0.0, 0.0, 0.0, 0.0, 120.0, 2920.0],
            laboratory: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
            consultations: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
            theatre: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
            admissions: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2000.0],
            general: [0.0, 0.0, 0.0, 0.0, 0.0, 44100.0, 7100.0],
            wallet: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 525305.0],
            total: [0.0, 0.0, 0.0, 0.0, 0.0, 44220.0, 537325.0]
        };

        // Monthly Trends Chart
        const trendsCtx = document.getElementById('trendsChart').getContext('2d');
        const trendsChart = new Chart(trendsCtx, {
            type: 'line',
            data: {
                labels: monthlyData.months,
                datasets: [{
                    label: 'Pharmacy',
                    data: monthlyData.pharmacy,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Laboratory',
                    data: monthlyData.laboratory,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Consultations',
                    data: monthlyData.consultations,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Theatre',
                    data: monthlyData.theatre,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Admissions',
                    data: monthlyData.admissions,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'General & Others',
                    data: monthlyData.general,
                    borderColor: '#6c757d',
                    backgroundColor: 'rgba(108, 117, 125, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: 'Wallet',
                    data: monthlyData.wallet,
                    borderColor: '#343a40',
                    backgroundColor: 'rgba(52, 58, 64, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₦' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₦' + context.parsed.y.toLocaleString();
                            }
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });

        // Revenue Distribution Chart
        const distributionCtx = document.getElementById('distributionChart').getContext('2d');
        const distributionChart = new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['Wallet Transactions', 'General & Others', 'Pharmacy', 'Admissions', 'Laboratory', 'Consultations', 'Theatre'],
                datasets: [{
                    data: [525305, 7100, 2920, 2000, 0, 0, 0],
                    backgroundColor: [
                        '#343a40',  // Wallet (dark)
                        '#6c757d',  // General (secondary)
                        '#007bff',  // Pharmacy (primary)
                        '#dc3545',  // Admissions (danger)
                        '#28a745',  // Laboratory (success)
                        '#17a2b8',  // Consultations (info)
                        '#ffc107'   // Theatre (warning)
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : '0';
                                return context.label + ': ₦' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15
                        }
                    }
                },
                cutout: '50%'
            }
        });
    </script>
</body>
</html>