{% extends 'base.html' %}

{% block title %}ENT Record Details - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">ENT Record Details</h4>
                <div>
                    <a href="{% url 'ent:create_prescription_for_ent' record.id %}" class="btn btn-success">
                        <i class="fas fa-prescription"></i> Create Prescription
                    </a>
                    <a href="{% url 'ent:edit_ent_record' record.id %}" class="btn btn-light">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'ent:ent_records_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> 
                            <a href="{% url 'patients:detail' record.patient.id %}">
                                {{ record.patient.get_full_name }}
                            </a>
                        </p>
                        <p><strong>Patient ID:</strong> {{ record.patient.patient_id }}</p>
                        <p><strong>Age:</strong> {{ record.patient.get_age }} years</p>
                        <p><strong>Gender:</strong> {{ record.patient.get_gender_display }}</p>
                        <p><strong>Phone:</strong> {{ record.patient.phone_number|default:"Not provided" }}</p>
                        
                        <h5 class="mt-4">Doctor Information</h5>
                        {% if record.doctor %}
                            <p><strong>Name:</strong> Dr. {{ record.doctor.get_full_name }}</p>
                        {% else %}
                            <p><strong>Name:</strong> Not assigned</p>
                        {% endif %}
                        
                        <h5 class="mt-4">Visit Information</h5>
                        <p><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y H:i" }}</p>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Chief Complaint</h5>
                        <p>{{ record.chief_complaint|default:"Not provided"|linebreaks }}</p>
                        
                        <h5 class="mt-4">History of Present Illness</h5>
                        <p>{{ record.history_of_present_illness|default:"Not provided"|linebreaks }}</p>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Examination Findings</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>External Ear</h6>
                                <p><strong>Right:</strong> {{ record.external_ear_right|default:"Not examined"|linebreaks }}</p>
                                <p><strong>Left:</strong> {{ record.external_ear_left|default:"Not examined"|linebreaks }}</p>
                                
                                <h6 class="mt-3">Ear Canal</h6>
                                <p><strong>Right:</strong> {{ record.ear_canal_right|default:"Not examined"|linebreaks }}</p>
                                <p><strong>Left:</strong> {{ record.ear_canal_left|default:"Not examined"|linebreaks }}</p>
                                
                                <h6 class="mt-3">Tympanic Membrane</h6>
                                <p><strong>Right:</strong> {{ record.tympanic_membrane_right|default:"Not examined"|linebreaks }}</p>
                                <p><strong>Left:</strong> {{ record.tympanic_membrane_left|default:"Not examined"|linebreaks }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Nose Examination</h6>
                                <p>{{ record.nose_examination|default:"Not examined"|linebreaks }}</p>
                                
                                <h6 class="mt-3">Throat Examination</h6>
                                <p>{{ record.throat_examination|default:"Not examined"|linebreaks }}</p>
                                
                                <h6 class="mt-3">Neck Examination</h6>
                                <p>{{ record.neck_examination|default:"Not examined"|linebreaks }}</p>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6>Audio Test Results</h6>
                                <p><strong>Right:</strong> {{ record.audio_test_right|default:"Not tested"|linebreaks }}</p>
                                <p><strong>Left:</strong> {{ record.audio_test_left|default:"Not tested"|linebreaks }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Diagnosis & Treatment</h5>
                        <p><strong>Diagnosis:</strong> {{ record.diagnosis|default:"Not provided"|linebreaks }}</p>
                        <p><strong>Treatment Plan:</strong> {{ record.treatment_plan|default:"Not provided"|linebreaks }}</p>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Notes</h5>
                        <p>{{ record.notes|default:"No notes provided"|linebreaks }}</p>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Follow-up</h5>
                        <p>
                            <strong>Follow-up Required:</strong> 
                            {% if record.follow_up_required %}
                                <span class="badge bg-warning">Yes</span>
                                {% if record.follow_up_date %}
                                    <br><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}
                                {% endif %}
                            {% else %}
                                <span class="badge bg-success">No</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        {% if record.authorization_code %}
                            <h5>Authorization</h5>
                            <p><strong>Code:</strong> {{ record.authorization_code }}</p>
                        {% endif %}
                        
                        <h5 class="mt-3">Record Dates</h5>
                        <p><strong>Created:</strong> {{ record.created_at|date:"M d, Y H:i" }}</p>
                        {% if record.updated_at %}
                            <p><strong>Last Updated:</strong> {{ record.updated_at|date:"M d, Y H:i" }}</p>
                        {% endif %}
                    </div>
                </div>
                
                {% if prescriptions %}
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Recent Prescriptions</h5>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Prescription ID</th>
                                        <th>Date</th>
                                        <th>Diagnosis</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prescription in prescriptions %}
                                    <tr>
                                        <td>{{ prescription.id }}</td>
                                        <td>{{ prescription.prescription_date|date:"M d, Y" }}</td>
                                        <td>{{ prescription.diagnosis|truncatewords:5 }}</td>
                                        <td>
                                            <span class="badge bg-{% if prescription.status == 'dispensed' %}success{% elif prescription.status == 'pending' %}warning{% elif prescription.status == 'approved' %}primary{% else %}secondary{% endif %}">
                                                {{ prescription.get_status_display }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}