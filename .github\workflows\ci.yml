name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install system dependencies (Chromium for headless browser tests)
        run: |
          sudo apt-get update
          sudo apt-get install -y chromium-browser chromium-chromedriver libnss3 libgconf-2-4

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then
            pip install -r requirements.txt
          else
            pip install pytest selenium webdriver-manager
          fi

      - name: Run migrations (if any)
        run: |
          # Try migrations if manage.py exists; ignore failures to keep CI flexible
          if [ -f manage.py ]; then
            python manage.py migrate --noinput || true
          fi

      - name: Run tests
        env:
          CHROME_BIN: /usr/bin/chromium-browser
        run: |
          python -m pytest -q
