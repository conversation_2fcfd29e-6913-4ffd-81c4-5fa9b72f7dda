/* Patient Search Styles */

.patient-search-results {
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.patient-search-results .dropdown-item {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #eee;
}

.patient-search-results .dropdown-item:last-child {
    border-bottom: none;
}

.patient-search-results .dropdown-item:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.patient-search-results .dropdown-item strong {
    display: block;
    margin-bottom: 0.25rem;
}

.patient-search-results .dropdown-item .text-muted {
    font-size: 0.875rem;
}

.patient-search-container {
    position: relative;
    margin-bottom: 1rem;
}

.patient-search-container .form-control {
    border-radius: 0.375rem;
}

.patient-search-container .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .patient-search-results {
        max-height: 200px;
    }
    
    .patient-search-results .dropdown-item {
        padding: 0.375rem 0.75rem;
    }
    
    .patient-search-results .dropdown-item strong {
        font-size: 0.9rem;
    }
    
    .patient-search-results .dropdown-item .text-muted {
        font-size: 0.75rem;
    }
}