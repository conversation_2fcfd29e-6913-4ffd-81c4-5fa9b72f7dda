{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        
                <div class="form-group">
                    <label for="{{ form.cancer_type.id_for_label }}">Cancer Type</label>
                    {{ form.cancer_type|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.stage.id_for_label }}">Stage</label>
                    {{ form.stage|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.tumor_size.id_for_label }}"><PERSON><PERSON> (cm)</label>
                    {{ form.tumor_size|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.metastasis.id_for_label }}">Metastasis</label>
                    {{ form.metastasis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.treatment_protocol.id_for_label }}">Treatment Protocol</label>
                    {{ form.treatment_protocol|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.chemotherapy_cycle.id_for_label }}">Chemotherapy Cycle</label>
                    {{ form.chemotherapy_cycle|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.radiation_dose.id_for_label }}">Radiation Dose (Gy)</label>
                    {{ form.radiation_dose|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.surgery_details.id_for_label }}">Surgery Details</label>
                    {{ form.surgery_details|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.biopsy_results.id_for_label }}">Biopsy Results</label>
                    {{ form.biopsy_results|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.oncology_marker.id_for_label }}">Oncology Marker</label>
                    {{ form.oncology_marker|add_class:"form-control" }}
                </div>
                
<div class="form-group">
                            <label for="{{ form.patient.id_for_label }}">Patient *</label>
                            {{ form.patient|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.doctor.id_for_label }}">Doctor</label>
                            {{ form.doctor|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.visit_date.id_for_label }}">Visit Date *</label>
                            {{ form.visit_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.chief_complaint.id_for_label }}">Chief Complaint</label>
                    {{ form.chief_complaint|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.history_of_present_illness.id_for_label }}">History of Present Illness</label>
                    {{ form.history_of_present_illness|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.diagnosis.id_for_label }}">Diagnosis</label>
                    {{ form.diagnosis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.treatment_plan.id_for_label }}">Treatment Plan</label>
                    {{ form.treatment_plan|add_class:"form-control" }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_required.id_for_label }}">Follow-up Required</label>
                            {{ form.follow_up_required|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_date.id_for_label }}">Follow-up Date</label>
                            {{ form.follow_up_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}">Notes</label>
                    {{ form.notes|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Record
                </button>
                <a href="{% url 'oncology:oncology_records_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}