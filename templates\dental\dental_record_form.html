{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient.id_for_label }}" class="form-label">Patient *</label>
                                {{ form.patient }}
                                {% if form.patient.errors %}
                                    <div class="text-danger">{{ form.patient.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Treatment Notes *</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                        <div class="form-text">Detailed notes about the dental treatment provided.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'dental:dental_records' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize select2 for patient selection if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $('.select2').length) {
        $('.select2').select2({
            placeholder: "Select a patient",
            allowClear: true,
            ajax: {
                url: "{% url 'dental:search_dental_patients' %}",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        term: params.term
                    };
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function(item) {
                            return {
                                id: item.id,
                                text: item.text
                            };
                        })
                    };
                },
                cache: true
            },
            minimumInputLength: 2
        });
    }
});
</script>
{% endblock %}