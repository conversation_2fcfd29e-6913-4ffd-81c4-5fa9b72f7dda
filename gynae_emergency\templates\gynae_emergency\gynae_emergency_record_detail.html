{% extends 'base.html' %}
{% load static %}

{% block title %}Gynae_emergency Record Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gynae_emergency Record Details</h1>
        <div>
            <a href="{% url 'gynae_emergency:edit_gynae_emergency_record' record.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Record
            </a>
            <a href="{% url 'gynae_emergency:gynae_emergency_records_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Quick Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'gynae_emergency:edit_gynae_emergency_record' record.id %}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Record
                        </a>
                        <a href="{% url 'gynae_emergency:create_prescription_for_gynae_emergency' record.id %}" class="btn btn-primary">
                            <i class="fas fa-prescription"></i> Create Prescription
                        </a>
                        <a href="{% url 'gynae_emergency:gynae_emergency_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> All Records
                        </a>
                        <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteRecordModal">
                            <i class="fas fa-trash"></i> Delete Record
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patient Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Patient Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Name:</strong> {{ record.patient.get_full_name }}</p>
                    <p><strong>Patient ID:</strong> {{ record.patient.patient_id }}</p>
                    <p><strong>Age:</strong> {{ record.patient.age }} years</p>
                    <p><strong>Gender:</strong> {{ record.patient.get_gender_display }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y H:i" }}</p>
                    <p><strong>Doctor:</strong> {{ record.doctor.get_full_name|default:"Not specified" }}</p>
                    <p><strong>Follow-up Required:</strong> 
                        {% if record.follow_up_required %}
                            <span class="badge badge-warning">Yes</span>
                            {% if record.follow_up_date %}
                                <br><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}
                            {% endif %}
                        {% else %}
                            <span class="badge badge-secondary">No</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

        <!-- Specific Fields -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Gynae_emergency Specific Fields</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Emergency Type:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.emergency_type|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Pain Level (1-10):</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.pain_level|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Bleeding Amount:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.bleeding_amount|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Contractions:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.contractions|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Contraction Frequency:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.contraction_frequency|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Rupture of Membranes:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.rupture_of_membranes|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Fetal Movement:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.fetal_movement|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Vaginal Discharge:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.vaginal_discharge|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Emergency Intervention:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.emergency_intervention|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Stabilization Status:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.stabilization_status|default:"Not recorded" }}</p>
                </div>
            </div>
        </div>
    </div>
<!-- Chief Complaint -->
    {% if record.chief_complaint %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Chief Complaint</h6>
        </div>
        <div class="card-body">
            <p>{{ record.chief_complaint|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- History of Present Illness -->
    {% if record.history_of_present_illness %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">History of Present Illness</h6>
        </div>
        <div class="card-body">
            <p>{{ record.history_of_present_illness|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Diagnosis -->
    {% if record.diagnosis %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Diagnosis</h6>
        </div>
        <div class="card-body">
            <p>{{ record.diagnosis|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Treatment Plan -->
    {% if record.treatment_plan %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Treatment Plan</h6>
        </div>
        <div class="card-body">
            <p>{{ record.treatment_plan|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Notes -->
    {% if record.notes %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
        </div>
        <div class="card-body">
            <p>{{ record.notes|linebreaks }}</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteRecordModal" tabindex="-1" role="dialog" aria-labelledby="deleteRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRecordModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the gynae_emergency record for <strong>{{ record.patient.get_full_name }}</strong> dated {{ record.visit_date|date:"M d, Y" }}? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="{% url 'gynae_emergency:delete_gynae_emergency_record' record.id %}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Yes, Delete
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}