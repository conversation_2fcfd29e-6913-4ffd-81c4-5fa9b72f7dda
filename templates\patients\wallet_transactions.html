{% extends 'base.html' %}

{% block title %}Wallet Transactions - {{ patient.get_full_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Wallet Transactions for {{ patient.get_full_name }}</h1>
        <a href="{% url 'patients:wallet_dashboard' patient.id %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Wallet
        </a>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Transaction History</h6>
                </div>
                <div class="card-body">
                    {% if transactions %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Type</th>
                                    <th>Amount (₦)</th>
                                    <th>Description</th>
                                    <th>Balance (₦)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.timestamp|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        {% if transaction.transaction_type == 'credit' %}
                                        <span class="badge bg-success">Credit</span>
                                        {% elif transaction.transaction_type == 'debit' %}
                                        <span class="badge bg-danger">Debit</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ transaction.transaction_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.amount|floatformat:2 }}</td>
                                    <td>{{ transaction.description }}</td>
                                    <td>{{ transaction.balance_after|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No transactions found for this wallet.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}