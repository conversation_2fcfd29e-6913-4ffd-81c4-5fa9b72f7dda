{% extends 'base.html' %}

{% block title %}Oncology Record Details - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Oncology Record Details</h4>
                <div>
                    <a href="{% url 'oncology:edit_oncology_record' record.id %}" class="btn btn-light">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'oncology:oncology_records_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> 
                            <a href="{% url 'patients:detail' record.patient.id %}">
                                {{ record.patient.get_full_name }}
                            </a>
                        </p>
                        <p><strong>Patient ID:</strong> {{ record.patient.id }}</p>
                        
                        <h5 class="mt-4">Doctor Information</h5>
                        {% if record.doctor %}
                            <p><strong>Name:</strong> Dr. {{ record.doctor.get_full_name }}</p>
                        {% else %}
                            <p><strong>Name:</strong> Not assigned</p>
                        {% endif %}
                        
                        <h5 class="mt-4">Visit Information</h5>
                        <p><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y H:i" }}</p>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Diagnosis & Treatment</h5>
                        <p><strong>Cancer Type:</strong> {{ record.cancer_type|default:"Not specified" }}</p>
                        <p><strong>Stage:</strong> {{ record.stage|default:"Not specified" }}</p>
                        <p><strong>Tumor Size:</strong> {{ record.tumor_size|default:"Not measured" }} cm</p>
                        <p><strong>Metastasis:</strong> 
                            {% if record.metastasis %}
                                <span class="badge bg-danger">Yes</span>
                            {% else %}
                                <span class="badge bg-success">No</span>
                            {% endif %}
                        </p>
                        <p><strong>Diagnosis:</strong> {{ record.diagnosis|default:"Not provided" }}</p>
                        <p><strong>Treatment Plan:</strong> {{ record.treatment_plan|default:"Not provided" }}</p>
                        
                        <h5 class="mt-4">Follow-up</h5>
                        <p>
                            <strong>Follow-up Required:</strong> 
                            {% if record.follow_up_required %}
                                <span class="badge bg-warning">Yes</span>
                            {% else %}
                                <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </p>
                        {% if record.follow_up_required and record.follow_up_date %}
                            <p><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Treatment Details</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Treatment Protocol:</strong> {{ record.treatment_protocol|default:"Not specified" }}</p>
                                <p><strong>Chemotherapy Cycle:</strong> {{ record.chemotherapy_cycle|default:"Not applicable" }}</p>
                                <p><strong>Radiation Dose:</strong> {{ record.radiation_dose|default:"Not applicable" }} Gy</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Surgery Details:</strong> {{ record.surgery_details|default:"Not applicable" }}</p>
                                <p><strong>Biopsy Results:</strong> {{ record.biopsy_results|default:"Not performed" }}</p>
                                <p><strong>Oncology Marker:</strong> {{ record.oncology_marker|default:"Not tested" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Additional Information</h5>
                        <p><strong>Notes:</strong> {{ record.notes|default:"No notes" }}</p>
                        {% if record.authorization_code %}
                            <p><strong>Authorization Code:</strong> {{ record.authorization_code }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <p><strong>Created At:</strong> {{ record.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Last Updated:</strong> {{ record.updated_at|date:"M d, Y H:i" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}