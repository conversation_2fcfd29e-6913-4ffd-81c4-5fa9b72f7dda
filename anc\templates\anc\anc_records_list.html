{% extends 'base.html' %}
{% load static %}

{% block title %}Anc Records{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Anc Records</h1>
        <a href="{% url 'anc:create_anc_record' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Record
        </a>
    </div>

    <!-- Search Form -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" class="form-inline">
                <div class="form-group mr-2">
                    <input type="text" class="form-control" name="search" placeholder="Search patients or diagnosis..." value="{{ search_query }}">
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                {% if search_query %}
                <a href="{% url 'anc:anc_records_list' %}" class="btn btn-secondary ml-2">
                    <i class="fas fa-times"></i> Clear
                </a>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Records List -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Visit Date</th>
                            <th>Doctor</th>
                            <th>Diagnosis</th>
                            <th>Follow-up</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in page_obj %}
                        <tr>
                            <td>{{ record.patient.get_full_name }}</td>
                            <td>{{ record.visit_date|date:"M d, Y" }}</td>
                            <td>{{ record.doctor.get_full_name|default:"Not specified" }}</td>
                            <td>{{ record.diagnosis|truncatewords:10 }}</td>
                            <td>
                                {% if record.follow_up_required %}
                                <span class="badge badge-warning">Required</span>
                                {% if record.follow_up_date %}
                                <br><small>{{ record.follow_up_date|date:"M d, Y" }}</small>
                                {% endif %}
                                {% else %}
                                <span class="badge badge-secondary">Not required</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'anc:anc_record_detail' record.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{% url 'anc:edit_anc_record' record.id %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No anc records found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">Last</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}