{% extends 'base.html' %}

{% block title %}Delete Dental Record{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Dental Record</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>You are about to delete the dental record for <strong>{{ record.patient.get_full_name }}</strong>.</p>
                
                <p><strong>Record Details:</strong></p>
                <ul>
                    <li><strong>Created:</strong> {{ record.created_at|date:"M d, Y H:i" }}</li>
                    <li><strong>Patient ID:</strong> {{ record.patient.patient_id }}</li>
                </ul>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'dental:dental_records' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}