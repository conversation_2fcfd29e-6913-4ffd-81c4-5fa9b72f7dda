{% extends 'base.html' %}
{% load static %}

{% block title %}Labor Record Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Labor Record Details</h1>
        <div>
            <a href="{% url 'labor:edit_labor_record' record.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Record
            </a>
            <a href="{% url 'labor:labor_records_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Quick Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'labor:edit_labor_record' record.id %}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Record
                        </a>
                        <a href="{% url 'labor:create_prescription_for_labor' record.id %}" class="btn btn-primary">
                            <i class="fas fa-prescription"></i> Create Prescription
                        </a>
                        <a href="{% url 'labor:order_medical_pack_for_labor' record.id %}" class="btn btn-success">
                            <i class="fas fa-shopping-cart"></i> Order Medical Pack
                        </a>
                        <a href="{% url 'labor:labor_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> All Records
                        </a>
                        <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteRecordModal">
                            <i class="fas fa-trash"></i> Delete Record
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patient Information -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Patient Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Name:</strong> {{ record.patient.get_full_name }}</p>
                    <p><strong>Patient ID:</strong> {{ record.patient.patient_id }}</p>
                    <p><strong>Age:</strong> {{ record.patient.age }} years</p>
                    <p><strong>Gender:</strong> {{ record.patient.get_gender_display }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y H:i" }}</p>
                    <p><strong>Doctor:</strong> {{ record.doctor.get_full_name|default:"Not specified" }}</p>
                    <p><strong>Follow-up Required:</strong> 
                        {% if record.follow_up_required %}
                            <span class="badge badge-warning">Yes</span>
                            {% if record.follow_up_date %}
                                <br><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}
                            {% endif %}
                        {% else %}
                            <span class="badge badge-secondary">No</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

        <!-- Specific Fields -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Labor Specific Fields</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Onset Time:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.onset_time|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Presentation:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.presentation|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Fetal Heart Rate:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.fetal_heart_rate|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Cervical Dilation (cm):</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.cervical_dilation|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Effacement (%):</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.effacement|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Rupture of Membranes:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.rupture_of_membranes|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Rupture Time:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.rupture_time|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Mode of Delivery:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.mode_of_delivery|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Duration First Stage:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.duration_first_stage|default:"Not recorded" }}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>Placenta Delivery Time:</strong></p>
                </div>
                <div class="col-md-8">
                    <p>{{ record.placenta_delivery_time|default:"Not recorded" }}</p>
                </div>
            </div>
        </div>
    </div>
<!-- Chief Complaint -->
    {% if record.chief_complaint %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Chief Complaint</h6>
        </div>
        <div class="card-body">
            <p>{{ record.chief_complaint|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- History of Present Illness -->
    {% if record.history_of_present_illness %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">History of Present Illness</h6>
        </div>
        <div class="card-body">
            <p>{{ record.history_of_present_illness|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Diagnosis -->
    {% if record.diagnosis %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Diagnosis</h6>
        </div>
        <div class="card-body">
            <p>{{ record.diagnosis|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Treatment Plan -->
    {% if record.treatment_plan %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Treatment Plan</h6>
        </div>
        <div class="card-body">
            <p>{{ record.treatment_plan|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Notes -->
    {% if record.notes %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
        </div>
        <div class="card-body">
            <p>{{ record.notes|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Medical Pack Orders -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-shopping-cart"></i> Medical Pack Orders
            </h6>
        </div>
        <div class="card-body">
            {% if record.pack_orders.all %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Pack Name</th>
                                <th>Order Date</th>
                                <th>Status</th>
                                <th>Total Cost</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for pack_order in record.pack_orders.all %}
                            <tr>
                                <td>
                                    <strong>{{ pack_order.pack.name }}</strong><br>
                                    <small class="text-muted">{{ pack_order.pack.get_pack_type_display }}</small>
                                </td>
                                <td>{{ pack_order.order_date|date:"M d, Y H:i" }}</td>
                                <td>
                                    {% if pack_order.status == 'pending' %}
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    {% elif pack_order.status == 'approved' %}
                                        <span class="badge bg-info">Approved</span>
                                    {% elif pack_order.status == 'processing' %}
                                        <span class="badge bg-primary">Processing</span>
                                    {% elif pack_order.status == 'ready' %}
                                        <span class="badge bg-success">Ready</span>
                                    {% elif pack_order.status == 'dispensed' %}
                                        <span class="badge bg-secondary">Dispensed</span>
                                    {% elif pack_order.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                    {% endif %}
                                </td>
                                <td class="text-success">₦{{ pack_order.pack.get_total_cost|floatformat:2 }}</td>
                                <td>
                                    <a href="{% url 'pharmacy:pack_order_detail' pack_order.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No medical pack orders for this labor record.</p>
                    <a href="{% url 'labor:order_medical_pack_for_labor' record.id %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Order Medical Pack
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteRecordModal" tabindex="-1" role="dialog" aria-labelledby="deleteRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRecordModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the labor record for <strong>{{ record.patient.get_full_name }}</strong> dated {{ record.visit_date|date:"M d, Y" }}? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="{% url 'labor:delete_labor_record' record.id %}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Yes, Delete
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}