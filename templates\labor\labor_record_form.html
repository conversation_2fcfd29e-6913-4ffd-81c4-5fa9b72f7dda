{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'labor:labor_records_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.patient|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.doctor|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.visit_date|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.authorization_code|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.onset_time|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.presentation|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.fetal_heart_rate|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.cervical_dilation|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.effacement|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.rupture_of_membranes|as_crispy_field }}
                        </div>
                    </div>
                    
                    {% if form.rupture_of_membranes.value %}
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.rupture_time|as_crispy_field }}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.mode_of_delivery|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.duration_first_stage|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.placenta_delivery_time|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.diagnosis|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.treatment_plan|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.follow_up_required|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.follow_up_date|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            {{ form.notes|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Record
                        </button>
                        <a href="{% url 'labor:labor_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}