{% extends 'base.html' %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <div>
                    <a href="{% url 'core:create_prescription' patient.id 'General' %}" class="btn btn-light me-2">
                        <i class="fas fa-plus"></i> New Prescription
                    </a>
                    <a href="{% url 'patients:detail' patient.id %}" class="btn btn-light">
                        <i class="fas fa-arrow-left"></i> Back to Patient
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> {{ patient.get_full_name }}</p>
                        <p><strong>Patient ID:</strong> {{ patient.patient_id }}</p>
                        <p><strong>Age:</strong> {{ patient.age }} years</p>
                        <p><strong>Gender:</strong> {{ patient.get_gender_display }}</p>
                    </div>
                </div>

                {% if prescriptions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Prescription ID</th>
                                <th>Date</th>
                                <th>Doctor</th>
                                <th>Diagnosis</th>
                                <th>Status</th>
                                <th>Items</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prescription in prescriptions %}
                            <tr>
                                <td>{{ prescription.id }}</td>
                                <td>{{ prescription.prescription_date|date:"M d, Y" }}</td>
                                <td>Dr. {{ prescription.doctor.get_full_name }}</td>
                                <td>{{ prescription.diagnosis|truncatewords:5 }}</td>
                                <td>
                                    {% if prescription.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif prescription.status == 'approved' %}
                                        <span class="badge bg-info">Approved</span>
                                    {% elif prescription.status == 'dispensed' %}
                                        <span class="badge bg-success">Dispensed</span>
                                    {% elif prescription.status == 'partially_dispensed' %}
                                        <span class="badge bg-primary">Partially Dispensed</span>
                                    {% elif prescription.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                    {% elif prescription.status == 'on_hold' %}
                                        <span class="badge bg-secondary">On Hold</span>
                                    {% endif %}
                                </td>
                                <td>{{ prescription.items.count }}</td>
                                <td>
                                    <a href="{% url 'pharmacy:prescription_detail' prescription.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-prescription fa-3x text-muted mb-3"></i>
                    <h5>No prescriptions found</h5>
                    <p class="text-muted">This patient doesn't have any prescriptions yet.</p>
                    <a href="{% url 'core:create_prescription' patient.id 'General' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create First Prescription
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}