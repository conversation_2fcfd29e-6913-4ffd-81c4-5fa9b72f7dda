{% extends 'base.html' %}

{% block title %}Delete Family Planning Record - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">Delete Family Planning Record</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                
                <p>You are about to delete the family planning record for <strong>{{ record.patient.get_full_name }}</strong>.</p>
                
                <div class="card mb-3">
                    <div class="card-body">
                        <h5>Record Details:</h5>
                        <ul class="list-unstyled">
                            <li><strong>Record ID:</strong> {{ record.id }}</li>
                            <li><strong>Created:</strong> {{ record.created_at|date:"M d, Y H:i" }}</li>
                            <li><strong>Method Used:</strong> {{ record.method_used|default:"Not specified" }}</li>
                            <li><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y" }}</li>
                        </ul>
                    </div>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'family_planning:family_planning_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Delete Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}