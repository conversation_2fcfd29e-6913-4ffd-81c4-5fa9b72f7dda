# Generated by Django 5.2 on 2025-08-17 17:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ophthalmic', '0002_ophthalmicrecord_authorization_code'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='clinical_findings',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='iop_left',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='iop_right',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='refraction_left_axis',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='refraction_left_cylinder',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='refraction_left_sphere',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='refraction_right_axis',
        ),
        migrations.Remove<PERSON>ield(
            model_name='ophthalmicrecord',
            name='refraction_right_cylinder',
        ),
        migrations.RemoveField(
            model_name='ophthalmicrecord',
            name='refraction_right_sphere',
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='anterior_chamber_left',
            field=models.TextField(blank=True, help_text='Anterior chamber - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='anterior_chamber_right',
            field=models.TextField(blank=True, help_text='Anterior chamber - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='conjunctiva_exam_left',
            field=models.TextField(blank=True, help_text='Conjunctiva examination - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='conjunctiva_exam_right',
            field=models.TextField(blank=True, help_text='Conjunctiva examination - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='cornea_exam_left',
            field=models.TextField(blank=True, help_text='Cornea examination - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='cornea_exam_right',
            field=models.TextField(blank=True, help_text='Cornea examination - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='eyelid_exam_left',
            field=models.TextField(blank=True, help_text='Eyelid examination - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='eyelid_exam_right',
            field=models.TextField(blank=True, help_text='Eyelid examination - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='fundus_exam_left',
            field=models.TextField(blank=True, help_text='Fundus examination - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='fundus_exam_right',
            field=models.TextField(blank=True, help_text='Fundus examination - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='intraocular_pressure_left',
            field=models.CharField(blank=True, help_text='Intraocular pressure - Left eye', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='intraocular_pressure_right',
            field=models.CharField(blank=True, help_text='Intraocular pressure - Right eye', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='lens_exam_left',
            field=models.TextField(blank=True, help_text='Lens examination - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='lens_exam_right',
            field=models.TextField(blank=True, help_text='Lens examination - Right eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='pupil_reaction_left',
            field=models.TextField(blank=True, help_text='Pupil reaction - Left eye', null=True),
        ),
        migrations.AddField(
            model_name='ophthalmicrecord',
            name='pupil_reaction_right',
            field=models.TextField(blank=True, help_text='Pupil reaction - Right eye', null=True),
        ),
        migrations.AlterField(
            model_name='ophthalmicrecord',
            name='visual_acuity_left',
            field=models.CharField(blank=True, help_text='Visual acuity - Left eye', max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='ophthalmicrecord',
            name='visual_acuity_right',
            field=models.CharField(blank=True, help_text='Visual acuity - Right eye', max_length=50, null=True),
        ),
    ]
