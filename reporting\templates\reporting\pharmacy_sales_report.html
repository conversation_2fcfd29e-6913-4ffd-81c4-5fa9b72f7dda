{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Sales</h6>
        </div>
        <div class="card-body">
            <form method="get">
                <div class="row">
                    <div class="col-md-4">{{ form.start_date.label_tag }} {{ form.start_date }}</div>
                    <div class="col-md-4">{{ form.end_date.label_tag }} {{ form.end_date }}</div>
                    <div class="col-md-4">{{ form.patient.label_tag }} {{ form.patient }}</div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Filter</button>
            </form>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Sales List</h6>
            <div class="font-weight-bold">Total Sales: ₦{{ total_sales|floatformat:2 }}</div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Patient</th>
                            <th>Date</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for prescription in page_obj %}
                        <tr>
                            <td>{{ prescription.id }}</td>
                            <td>{{ prescription.patient.get_full_name }}</td>
                            <td>{{ prescription.prescription_date }}</td>
                            <td>₦{{ prescription.get_total_prescribed_price|floatformat:2 }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4" class="text-center">No sales found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% include 'pagination.html' %}
        </div>
    </div>
</div>
{% endblock %}