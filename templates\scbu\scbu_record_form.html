{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'scbu:scbu_records_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.patient|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.doctor|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.visit_date|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.authorization_code|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.gestational_age|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.birth_weight|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.apgar_score_1min|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.apgar_score_5min|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.respiratory_support|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.ventilation_type|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.feeding_method|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.infection_status|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.antibiotic_name|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.discharge_weight|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.diagnosis|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.treatment_plan|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.follow_up_required|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.follow_up_date|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            {{ form.notes|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Record
                        </button>
                        <a href="{% url 'scbu:scbu_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}