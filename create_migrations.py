import os
import datetime

# Define the modules
modules = [
    'ent',
    'oncology',
    'scbu',
    'anc',
    'labor',
    'icu',
    'family_planning',
    'gynae_emergency'
]

# Define the base path
base_path = os.getcwd()

# Create migration files for each module
timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")

for module in modules:
    migrations_path = os.path.join(base_path, module, 'migrations')
    
    # Create migration file
    migration_content = f'''# Generated by Django 5.2 on {timestamp}

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('{module}', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='{module}record',
            name='authorization_code',
            field=models.CharField(blank=True, help_text='Authorization code from desk office', max_length=50, null=True),
        ),
    ]
'''
    
    # Write the migration file
    migration_file = os.path.join(migrations_path, f'0002_{module}record_authorization_code.py')
    with open(migration_file, 'w') as f:
        f.write(migration_content)

print("Migration files created for all modules successfully!")