{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Labor Record{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Delete Labor Record</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="alert alert-warning">
                <h4 class="alert-heading">Warning!</h4>
                <p>Are you sure you want to delete the labor record for <strong>{{ record.patient.get_full_name }}</strong> dated {{ record.visit_date|date:"M d, Y" }}?</p>
                <p>This action cannot be undone.</p>
            </div>
            
            <form method="POST">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Yes, Delete
                </button>
                <a href="{% url 'labor:labor_record_detail' record.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}