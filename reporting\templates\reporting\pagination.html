{% if page_obj.has_other_pages %}
<nav aria-label="Page navigation">
  <ul class="pagination justify-content-center">
    {% if page_obj.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <span class="page-link">&laquo;</span>
      </li>
    {% endif %}
    {% for num in page_obj.paginator.page_range %}
      {% if page_obj.number == num %}
        <li class="page-item active"><span class="page-link">{{ num }}</span></li>
      {% else %}
        <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
      {% endif %}
    {% endfor %}
    {% if page_obj.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <span class="page-link">&raquo;</span>
      </li>
    {% endif %}
  </ul>
</nav>
{% endif %}
