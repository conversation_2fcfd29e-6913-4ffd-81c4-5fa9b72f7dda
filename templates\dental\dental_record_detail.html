{% extends 'base.html' %}

{% block title %}Dental Record Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Dental Record Details</h1>
        <div>
            <a href="{% url 'dental:edit_dental_record' record.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Record
            </a>
            <a href="{% url 'dental:dental_records' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <!-- Quick Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'dental:edit_dental_record' record.id %}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Record
                        </a>
                        <a href="{% url 'dental:create_prescription_for_dental' record.id %}" class="btn btn-primary">
                            <i class="fas fa-prescription"></i> Create Prescription
                        </a>
                        <a href="{% url 'dental:dental_records' %}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> All Records
                        </a>
                        <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteRecordModal">
                            <i class="fas fa-trash"></i> Delete Record
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> 
                            <a href="{% url 'patients:detail' record.patient.id %}">
                                {{ record.patient.get_full_name }}
                            </a>
                        </p>
                        <p><strong>Patient ID:</strong> {{ record.patient.patient_id }}</p>
                        <p><strong>Age:</strong> {{ record.patient.get_age }} years</p>
                        <p><strong>Gender:</strong> {{ record.patient.get_gender_display }}</p>
                        <p><strong>Phone:</strong> {{ record.patient.phone_number|default:"Not provided" }}</p>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Record Information</h5>
                        <p><strong>Created:</strong> {{ record.created_at|date:"M d, Y H:i" }}</p>
                        {% if record.updated_at %}
                            <p><strong>Last Updated:</strong> {{ record.updated_at|date:"M d, Y H:i" }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Treatment Notes</h5>
                        <div class="border p-3 bg-light">
                            {{ record.notes|linebreaks|default:"No notes provided" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteRecordModal" tabindex="-1" role="dialog" aria-labelledby="deleteRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRecordModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete the dental record for <strong>{{ record.patient.get_full_name }}</strong> created on {{ record.created_at|date:"M d, Y" }}? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="{% url 'dental:delete_dental_record' record.id %}" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Yes, Delete
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}