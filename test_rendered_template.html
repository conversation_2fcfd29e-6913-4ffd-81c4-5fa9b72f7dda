<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

    <!-- Custom CSS -->
    

    <link rel="stylesheet" href="/static/css/style_new.css">
    <link rel="stylesheet" href="/static/css/patient_search.css">

    
    <style>
        /* Main layout for sidebar and content */
        html, body {
            height: 100%;
            overflow-x: hidden; /* Allow horizontal scrolling if needed */
            overflow-y: auto; /* Allow vertical scrolling */
        }
        #wrapper {
            display: flex;
            min-height: 100vh;
            width: 100vw;
            background: #f8f9fc;
            overflow-x: hidden; /* Keep horizontal hidden if desired, but allow vertical */
            overflow-y: auto; /* Allow vertical scrolling */
        }

        /* Content Wrapper */
        #content-wrapper {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            width: 100%;
            min-width: 0;
            background: #f8f9fc;
            overflow-y: auto; /* Changed from hidden to auto */
            height: 100vh;
        }

        /* Main Content Area */
        #content {
            flex: 1 0 auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px; /* Space for footer */
            overflow-y: auto;
            height: 100vh;
        }

        /* Container Fluid for content padding */
        .container-fluid {
            flex: 1 0 auto;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            padding: 1rem;
        }

        /* Topbar custom style */
        .topbar {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            border-radius: 0 0 12px 12px;
            padding: 0.5rem 2rem;
            min-height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1020;
        }
        .topbar .navbar-brand {
            font-weight: 700;
            color: #00bfff !important;
            font-size: 1.4rem;
            letter-spacing: 1px;
        }
        .topbar .nav-link, .topbar .fa {
            color: #23272b !important;
            font-size: 1.1rem;
        }
        .topbar .nav-link:hover {
            color: #00bfff !important;
        }
        /* Footer custom style */
        footer, .footer {
            flex-shrink: 0;
            background: #fff;
            color: #23272b;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
            border-radius: 12px 12px 0 0;
            padding: 1rem 2rem;
            text-align: center;
            font-size: 1rem;
            margin-top: auto;
            position: sticky;
            bottom: 0;
            width: 100%;
        }
        /* Responsive tweaks */
        @media (max-width: 991.98px) {
            .topbar, footer, .footer {
                padding: 0.5rem 1rem;
                border-radius: 0;
            }
        }
        @media (max-width: 600px) {
            .topbar, footer, .footer {
                padding: 0.5rem 0.5rem;
                font-size: 0.95rem;
            }
            .topbar .navbar-brand {
                font-size: 1.1rem;
            }
        }

        /* Auto Logout Modal Styles */
        #logoutWarningModal .modal-content {
            border: 2px solid #ffc107;
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
        }

        #logoutWarningModal #countdownTimer {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        #logoutModal .modal-content {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
        }

        .modal-backdrop.show {
            opacity: 0.7;
        }

        /* Session Status Indicator */
        #sessionStatus {
            cursor: default;
            font-size: 0.9rem;
        }

        #sessionIndicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
    <script>
        // Sidebar toggle for mobile - handled by sidebar.js
    </script>
    
    
    <!-- Additional head content here -->
    
</head>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        
        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <a class="navbar-brand" href="/dashboard/">HMS</a>
                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ms-auto">
                        
                            <li class="nav-item">
                                <a class="nav-link" href="/accounts/login/">
                                    <span class="me-2 d-none d-lg-inline text-gray-600 small">Login</span>
                                    <i class="fas fa-sign-in-alt"></i>
                                </a>
                            </li>
                        
                    </ul>
                </nav>
                <!-- End of Topbar -->
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    
                    
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"></h3>
        </div>
        <div class="card-body">
          <!-- Date Range Filter -->
          <form method="GET" class="mb-4">
            <div class="row">
              <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="">
              </div>
              <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="">
              </div>
              <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">Filter</button>
              </div>
            </div>
          </form>

          <!-- Summary Cards -->
          <div class="row mb-4">
            <div class="col-md-4">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <h5 class="card-title">Total Revenue</h5>
                  <p class="card-text display-6">₦825</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts -->
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">Daily Revenue Trend</h5>
                </div>
                <div class="card-body">
                  <canvas id="dailyRevenueChart" height="100"></canvas>
                </div>
              </div>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">Top Medications by Revenue</h5>
                </div>
                <div class="card-body">
                  <canvas id="medicationRevenueChart" height="100"></canvas>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">Top Doctors by Revenue</h5>
                </div>
                <div class="card-body">
                  <canvas id="doctorRevenueChart" height="100"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Tables -->
          <div class="row">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">Medication Revenue Details</h5>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Medication</th>
                            <th>Revenue (₦)</th>
                        </tr>
                      </thead>
                      <tbody>
                        
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">Doctor Revenue Details</h5>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Doctor</th>
                          <th>Revenue (₦)</th>
                        </tr>
                      </thead>
                      <tbody>
                        
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->
            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>&copy; 2025 Hospital Management System. All rights reserved.</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="/accounts/logout/" style="display:inline;">
                        
                        <button type="submit" class="btn btn-primary">Logout</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- jQuery Easing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.4.1/jquery.easing.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/sidebar.js"></script>
    <script src="/static/js/patient_search.js"></script>

    <!-- Modal Fix Script -->
    <script>
        $(document).ready(function() {
            // Ensure modals work properly with custom CSS
            $('.modal').on('show.bs.modal', function (e) {
                $('body').addClass('modal-open');
                $('#wrapper').addClass('modal-open');

                // Ensure modal is properly positioned
                $(this).css({
                    'display': 'block',
                    'pointer-events': 'auto'
                });

                // Initialize Select2 for elements in the modal
                $(this).find('.select2').select2({
                    dropdownParent: $(this),
                    theme: 'bootstrap-5'
                });
            });

            $('.modal').on('shown.bs.modal', function (e) {
                // Ensure modal content is interactive
                $(this).find('.modal-content').css('pointer-events', 'auto');
                $(this).find('button, input, select, textarea, a').css('pointer-events', 'auto');

                // Focus on first input after modal is fully shown
                setTimeout(() => {
                    $(this).find('input, select, textarea').first().focus();
                }, 100);
            });

            $('.modal').on('hidden.bs.modal', function (e) {
                $('body').removeClass('modal-open');
                $('#wrapper').removeClass('modal-open');

                // Reset pointer events
                $(this).css('pointer-events', '');

                // Destroy Select2 instances to prevent memory leaks
                $(this).find('.select2').each(function() {
                    if ($(this).hasClass('select2-hidden-accessible')) {
                        $(this).select2('destroy');
                    }
                });
            });

            // Fix for modal backdrop click issues
            $('.modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).modal('hide');
                }
            });

            // Handle clicks outside modal content to close modal
            $(document).on('click', '.modal', function(e) {
                if ($(e.target).hasClass('modal')) {
                    $(this).modal('hide');
                }
            });

            // Ensure modal content is clickable
            $('.modal-content').on('click', function(e) {
                e.stopPropagation();
            });

            // Prevent form submission on Enter key in modal inputs (except textareas)
            $('.modal').on('keypress', 'input:not([type="submit"])', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    return false;
                }
            });

            // Additional fix for modal interaction issues
            $(document).on('click', '.modal-backdrop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.modal.show').modal('hide');
            });

            // Handle ESC key to close modal
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('.modal.show').length > 0) {
                    $('.modal.show').modal('hide');
                }
            });

            // Ensure buttons in modals are clickable
            $(document).on('click', '.modal button', function(e) {
                e.stopPropagation();
            });
        });
    </script>

    <!-- Auto Logout for Inactive Users -->
    <script>
        $(document).ready(function() {
            // Auto logout configuration
            const INACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes in milliseconds
            const WARNING_TIME = 1 * 60 * 1000; // Show warning 1 minute before logout

            let inactivityTimer;
            let warningTimer;
            let warningShown = false;

            // Events that reset the inactivity timer
            const resetEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

            // Function to update session status indicator
            function updateSessionStatus(status) {
                const indicator = $('#sessionIndicator');
                const text = $('#sessionText');

                switch(status) {
                    case 'active':
                        indicator.removeClass('text-warning text-danger').addClass('text-success');
                        text.text('Active');
                        break;
                    case 'warning':
                        indicator.removeClass('text-success text-danger').addClass('text-warning');
                        text.text('Warning');
                        break;
                    case 'expired':
                        indicator.removeClass('text-success text-warning').addClass('text-danger');
                        text.text('Expired');
                        break;
                }
            }

            // Function to show logout warning
            function showLogoutWarning() {
                if (warningShown) return;
                warningShown = true;
                updateSessionStatus('warning');

                // Create warning modal
                const warningModal = `
                    <div class="modal fade" id="logoutWarningModal" tabindex="-1" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-warning">
                                <div class="modal-header bg-warning text-dark">
                                    <h5 class="modal-title">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Session Timeout Warning
                                    </h5>
                                </div>
                                <div class="modal-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                        <h6>Your session will expire in:</h6>
                                        <h3 class="text-danger" id="countdownTimer">01:00</h3>
                                    </div>
                                    <p class="text-muted">You will be automatically logged out due to inactivity. Click "Stay Logged In" to continue your session.</p>
                                </div>
                                <div class="modal-footer justify-content-center">
                                    <button type="button" class="btn btn-success" id="stayLoggedInBtn">
                                        <i class="fas fa-user-check me-1"></i>Stay Logged In
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="logoutNowBtn">
                                        <i class="fas fa-sign-out-alt me-1"></i>Logout Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add modal to page if it doesn't exist
                if (!$('#logoutWarningModal').length) {
                    $('body').append(warningModal);
                }

                // Show the modal
                $('#logoutWarningModal').modal('show');

                // Start countdown
                let timeLeft = 60; // 60 seconds
                const countdownInterval = setInterval(function() {
                    timeLeft--;
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    $('#countdownTimer').text(
                        String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0')
                    );

                    if (timeLeft <= 0) {
                        clearInterval(countdownInterval);
                        performLogout();
                    }
                }, 1000);

                // Handle stay logged in button
                $('#stayLoggedInBtn').off('click').on('click', function() {
                    clearInterval(countdownInterval);
                    $('#logoutWarningModal').modal('hide');
                    warningShown = false;
                    updateSessionStatus('active');
                    resetInactivityTimer();
                });

                // Handle logout now button
                $('#logoutNowBtn').off('click').on('click', function() {
                    clearInterval(countdownInterval);
                    performLogout();
                });

                // Clean up when modal is hidden
                $('#logoutWarningModal').on('hidden.bs.modal', function() {
                    if (!warningShown) {
                        clearInterval(countdownInterval);
                    }
                });
            }

            // Function to perform logout
            function performLogout() {
                updateSessionStatus('expired');

                // Clear any existing modals
                $('#logoutWarningModal').modal('hide');

                // Show logout message
                const logoutMessage = `
                    <div class="modal fade" id="logoutModal" tabindex="-1" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-warning">
                                <div class="modal-header bg-warning text-dark">
                                    <h5 class="modal-title">
                                        <i class="fas fa-exclamation-triangle me-2"></i>Session Expired
                                    </h5>
                                </div>
                                <div class="modal-body text-center py-4">
                                    <div class="mb-3">
                                        <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                        <h5>Session Timeout</h5>
                                        <p class="text-muted">You have been automatically logged out due to inactivity.</p>
                                        <p class="text-info">You will be redirected to the login page in <span id="redirectCountdown">3</span> seconds...</p>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary" onclick="redirectToLogin()">
                                            <i class="fas fa-sign-in-alt me-2"></i>Go to Login Page Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(logoutMessage);
                $('#logoutModal').modal('show');

                // Start countdown for redirect
                let redirectTime = 3;
                const redirectInterval = setInterval(function() {
                    redirectTime--;
                    $('#redirectCountdown').text(redirectTime);

                    if (redirectTime <= 0) {
                        clearInterval(redirectInterval);
                        redirectToLogin();
                    }
                }, 1000);
            }

            // Function to redirect to login page
            function redirectToLogin() {
                // First, try to logout via AJAX to clear server session
                $.ajax({
                    url: '/accounts/logout/',
                    type: 'POST',
                    headers: {
                        'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val() ||
                                      $('meta[name=csrf-token]').attr('content') ||
                                      getCookie('csrftoken')
                    },
                    complete: function() {
                        // Always redirect to login page regardless of AJAX result
                        window.location.href = '/accounts/login/?auto_logout=1&reason=inactivity';
                    }
                });
            }

            // Function to get CSRF cookie
            function getCookie(name) {
                let cookieValue = null;
                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }
                return cookieValue;
            }

            // Function to reset inactivity timer
            function resetInactivityTimer() {
                // Clear existing timers
                clearTimeout(inactivityTimer);
                clearTimeout(warningTimer);

                // Set warning timer (show warning 1 minute before logout)
                warningTimer = setTimeout(function() {
                    showLogoutWarning();
                }, INACTIVITY_TIMEOUT - WARNING_TIME);

                // Set logout timer
                inactivityTimer = setTimeout(function() {
                    if (!warningShown) {
                        performLogout();
                    }
                }, INACTIVITY_TIMEOUT);
            }

            // Bind events to reset timer
            resetEvents.forEach(function(event) {
                document.addEventListener(event, function() {
                    if (!warningShown) {
                        resetInactivityTimer();
                    }
                }, true);
            });

            // Initialize the timer and session status
            resetInactivityTimer();
            updateSessionStatus('active');

            // Handle page visibility change (when user switches tabs)
            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'visible' && !warningShown) {
                    resetInactivityTimer();
                }
            });

            // Make redirectToLogin globally accessible
            window.redirectToLogin = redirectToLogin;

            // Handle browser/tab close events
            window.addEventListener('beforeunload', function(e) {
                if (warningShown) {
                    // If warning is shown, prevent default browser behavior
                    e.preventDefault();
                    e.returnValue = 'Your session is about to expire. Are you sure you want to leave?';
                    return e.returnValue;
                }
            });

            // Handle network connectivity issues
            window.addEventListener('online', function() {
                if (!warningShown) {
                    resetInactivityTimer();
                }
            });

            window.addEventListener('offline', function() {
                // Pause timers when offline
                clearTimeout(inactivityTimer);
                clearTimeout(warningTimer);
            });

        });
    </script>

    
<script src="/static/js/chart.min.js"></script>
<script>
  // Daily Revenue Chart
  const dailyRevenueCtx = document.getElementById('dailyRevenueChart').getContext('2d');
  const dailyRevenueChart = new Chart(dailyRevenueCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Daily Revenue (₦)',
        data: [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Medication Revenue Chart
  const medicationRevenueCtx = document.getElementById('medicationRevenueChart').getContext('2d');
  const medicationRevenueChart = new Chart(medicationRevenueCtx, {
    type: 'bar',
    data: {
      labels: [],
      datasets: [{
        label: 'Revenue (₦)',
        data: [],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Doctor Revenue Chart
  const doctorRevenueCtx = document.getElementById('doctorRevenueChart').getContext('2d');
  const doctorRevenueChart = new Chart(doctorRevenueCtx, {
    type: 'bar',
    data: {
      labels: [],
      datasets: [{
        label: 'Revenue (₦)',
        data: [],
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
</script>


    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post">
                    
                    <input type="hidden" name="add_item" value="1">

                    <div class="modal-header">
                        <h5 class="modal-title" id="addItemModalLabel">Add Invoice Item</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="" class="form-label">Service</label>
                            
                            
                            <div class="form-text">Select a service or enter a custom description below.</div>
                        </div>

                        <div class="mb-3">
                            <label for="" class="form-label">Description</label>
                            
                            
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="" class="form-label">Quantity</label>
                                
                                
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="" class="form-label">Unit Price</label>
                                
                                
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
