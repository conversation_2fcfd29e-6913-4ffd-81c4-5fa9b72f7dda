{% extends 'base.html' %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ title }}</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.patient.id_for_label }}" class="form-label">Patient *</label>
                                {{ form.patient }}
                                {% if form.patient.errors %}
                                    <div class="text-danger">{{ form.patient.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.doctor.id_for_label }}" class="form-label">Doctor</label>
                                {{ form.doctor }}
                                {% if form.doctor.errors %}
                                    <div class="text-danger">{{ form.doctor.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.visit_date.id_for_label }}" class="form-label">Visit Date *</label>
                                {{ form.visit_date }}
                                {% if form.visit_date.errors %}
                                    <div class="text-danger">{{ form.visit_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.method_used.id_for_label }}" class="form-label">Method Used</label>
                                {{ form.method_used }}
                                {% if form.method_used.errors %}
                                    <div class="text-danger">{{ form.method_used.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">Start Date</label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger">{{ form.start_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">End Date</label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger">{{ form.end_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.refill_date.id_for_label }}" class="form-label">Refill Date</label>
                                {{ form.refill_date }}
                                {% if form.refill_date.errors %}
                                    <div class="text-danger">{{ form.refill_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.compliance.id_for_label }}" class="form-label">Compliance</label>
                                <div class="form-check">
                                    {{ form.compliance }}
                                    <label class="form-check-label" for="{{ form.compliance.id_for_label }}">
                                        Patient is compliant with the family planning method
                                    </label>
                                </div>
                                {% if form.compliance.errors %}
                                    <div class="text-danger">{{ form.compliance.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.partner_involvement.id_for_label }}" class="form-label">Partner Involvement</label>
                                <div class="form-check">
                                    {{ form.partner_involvement }}
                                    <label class="form-check-label" for="{{ form.partner_involvement.id_for_label }}">
                                        Partner is involved in family planning decisions
                                    </label>
                                </div>
                                {% if form.partner_involvement.errors %}
                                    <div class="text-danger">{{ form.partner_involvement.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.side_effects.id_for_label }}" class="form-label">Side Effects</label>
                        {{ form.side_effects }}
                        {% if form.side_effects.errors %}
                            <div class="text-danger">{{ form.side_effects.errors }}</div>
                        {% endif %}
                        <div class="form-text">Document any side effects experienced by the patient.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.education_provided.id_for_label }}" class="form-label">Education Provided</label>
                        {{ form.education_provided }}
                        {% if form.education_provided.errors %}
                            <div class="text-danger">{{ form.education_provided.errors }}</div>
                        {% endif %}
                        <div class="form-text">Details of education and counseling provided to the patient.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.discontinuation_reason.id_for_label }}" class="form-label">Discontinuation Reason</label>
                        {{ form.discontinuation_reason }}
                        {% if form.discontinuation_reason.errors %}
                            <div class="text-danger">{{ form.discontinuation_reason.errors }}</div>
                        {% endif %}
                        <div class="form-text">Reason for discontinuation if the patient stopped the method.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.diagnosis.id_for_label }}" class="form-label">Diagnosis</label>
                        {{ form.diagnosis }}
                        {% if form.diagnosis.errors %}
                            <div class="text-danger">{{ form.diagnosis.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.treatment_plan.id_for_label }}" class="form-label">Treatment Plan</label>
                        {{ form.treatment_plan }}
                        {% if form.treatment_plan.errors %}
                            <div class="text-danger">{{ form.treatment_plan.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.follow_up_required.id_for_label }}" class="form-label">Follow-up Required</label>
                                <div class="form-check">
                                    {{ form.follow_up_required }}
                                    <label class="form-check-label" for="{{ form.follow_up_required.id_for_label }}">
                                        Schedule follow-up appointment
                                    </label>
                                </div>
                                {% if form.follow_up_required.errors %}
                                    <div class="text-danger">{{ form.follow_up_required.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.follow_up_date.id_for_label }}" class="form-label">Follow-up Date</label>
                                {{ form.follow_up_date }}
                                {% if form.follow_up_date.errors %}
                                    <div class="text-danger">{{ form.follow_up_date.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.authorization_code.id_for_label }}" class="form-label">Authorization Code</label>
                        {{ form.authorization_code }}
                        {% if form.authorization_code.errors %}
                            <div class="text-danger">{{ form.authorization_code.errors }}</div>
                        {% endif %}
                        <div class="form-text">Authorization code from desk office if required.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger">{{ form.notes.errors }}</div>
                        {% endif %}
                        <div class="form-text">Additional notes and observations.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'family_planning:family_planning_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Record
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date pickers if needed
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // You can add date picker initialization here if needed
    });
    
    // Initialize datetime pickers if needed
    const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');
    datetimeInputs.forEach(input => {
        // You can add datetime picker initialization here if needed
    });
});
</script>
{% endblock %}