{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        
                <div class="form-group">
                    <label for="{{ form.onset_time.id_for_label }}">Onset Time</label>
                    {{ form.onset_time|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.presentation.id_for_label }}">Presentation</label>
                    {{ form.presentation|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.fetal_heart_rate.id_for_label }}">Fetal Heart Rate</label>
                    {{ form.fetal_heart_rate|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.cervical_dilation.id_for_label }}">Cervical Dilation (cm)</label>
                    {{ form.cervical_dilation|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.effacement.id_for_label }}">Effacement (%)</label>
                    {{ form.effacement|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.rupture_of_membranes.id_for_label }}">Rupture of Membranes</label>
                    {{ form.rupture_of_membranes|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.rupture_time.id_for_label }}">Rupture Time</label>
                    {{ form.rupture_time|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.mode_of_delivery.id_for_label }}">Mode of Delivery</label>
                    {{ form.mode_of_delivery|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.duration_first_stage.id_for_label }}">Duration First Stage</label>
                    {{ form.duration_first_stage|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.placenta_delivery_time.id_for_label }}">Placenta Delivery Time</label>
                    {{ form.placenta_delivery_time|add_class:"form-control" }}
                </div>
                
<div class="form-group">
                            <label for="{{ form.patient.id_for_label }}">Patient *</label>
                            {{ form.patient|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.doctor.id_for_label }}">Doctor</label>
                            {{ form.doctor|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.visit_date.id_for_label }}">Visit Date *</label>
                            {{ form.visit_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.chief_complaint.id_for_label }}">Chief Complaint</label>
                    {{ form.chief_complaint|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.history_of_present_illness.id_for_label }}">History of Present Illness</label>
                    {{ form.history_of_present_illness|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.diagnosis.id_for_label }}">Diagnosis</label>
                    {{ form.diagnosis|add_class:"form-control" }}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.treatment_plan.id_for_label }}">Treatment Plan</label>
                    {{ form.treatment_plan|add_class:"form-control" }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_required.id_for_label }}">Follow-up Required</label>
                            {{ form.follow_up_required|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.follow_up_date.id_for_label }}">Follow-up Date</label>
                            {{ form.follow_up_date|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.notes.id_for_label }}">Notes</label>
                    {{ form.notes|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Record
                </button>
                <a href="{% url 'labor:labor_records_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}