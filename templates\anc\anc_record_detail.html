{% extends 'base.html' %}

{% block title %}ANC Record Details - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">ANC Record Details</h4>
                <div>
                    <a href="{% url 'anc:create_prescription_for_anc' record.id %}" class="btn btn-success">
                        <i class="fas fa-prescription"></i> Create Prescription
                    </a>
                    <a href="{% url 'anc:edit_anc_record' record.id %}" class="btn btn-light">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{% url 'anc:anc_records_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Patient Information</h5>
                        <p><strong>Name:</strong> 
                            <a href="{% url 'patients:detail' record.patient.id %}">
                                {{ record.patient.get_full_name }}
                            </a>
                        </p>
                        <p><strong>Patient ID:</strong> {{ record.patient.patient_id }}</p>
                        <p><strong>Age:</strong> {{ record.patient.get_age }} years</p>
                        <p><strong>Gender:</strong> {{ record.patient.get_gender_display }}</p>
                        
                        <h5 class="mt-4">Doctor Information</h5>
                        {% if record.doctor %}
                            <p><strong>Name:</strong> Dr. {{ record.doctor.get_full_name }}</p>
                        {% else %}
                            <p><strong>Name:</strong> Not assigned</p>
                        {% endif %}
                        
                        <h5 class="mt-4">Visit Information</h5>
                        <p><strong>Visit Date:</strong> {{ record.visit_date|date:"M d, Y H:i" }}</p>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Pregnancy Information</h5>
                        <p><strong>Gravida:</strong> {{ record.gravida|default:"Not provided" }}</p>
                        <p><strong>Para:</strong> {{ record.para|default:"Not provided" }}</p>
                        <p><strong>Abortions:</strong> {{ record.abortions|default:"Not provided" }}</p>
                        <p><strong>LMP:</strong> {{ record.lmp|date:"M d, Y"|default:"Not provided" }}</p>
                        <p><strong>EDD:</strong> {{ record.edd|date:"M d, Y"|default:"Not provided" }}</p>
                        
                        <h5 class="mt-4">Examination</h5>
                        <p><strong>Fundal Height:</strong> {{ record.fundal_height|default:"Not provided" }} cm</p>
                        <p><strong>Fetal Heartbeat:</strong> 
                            {% if record.fetal_heartbeat %}Present{% else %}Absent{% endif %}
                        </p>
                        <p><strong>Fetal Position:</strong> {{ record.fetal_position|default:"Not provided" }}</p>
                        <p><strong>Blood Pressure:</strong> {{ record.blood_pressure|default:"Not provided" }}</p>
                        <p><strong>Urine Protein:</strong> {{ record.urine_protein|default:"Not provided" }}</p>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Diagnosis & Treatment</h5>
                        <p><strong>Diagnosis:</strong> {{ record.diagnosis|default:"Not provided" }}</p>
                        <p><strong>Treatment Plan:</strong> {{ record.treatment_plan|default:"Not provided" }}</p>
                        
                        <h5 class="mt-4">Notes</h5>
                        <p>{{ record.notes|default:"No notes provided"|linebreaks }}</p>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Follow-up</h5>
                        <p>
                            <strong>Follow-up Required:</strong> 
                            {% if record.follow_up_required %}
                                <span class="badge bg-warning">Yes</span>
                                {% if record.follow_up_date %}
                                    <br><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}
                                {% endif %}
                            {% else %}
                                <span class="badge bg-success">No</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
                                <span class="badge bg-warning">Yes</span>
                            {% else %}
                                <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </p>
                        {% if record.follow_up_required and record.follow_up_date %}
                            <p><strong>Follow-up Date:</strong> {{ record.follow_up_date|date:"M d, Y" }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>ANC Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Gravida:</strong> {{ record.gravida|default:"Not specified" }}</p>
                                <p><strong>Para:</strong> {{ record.para|default:"Not specified" }}</p>
                                <p><strong>Abortions:</strong> {{ record.abortions|default:"Not specified" }}</p>
                                <p><strong>Last Menstrual Period:</strong> {{ record.lmp|date:"M d, Y"|default:"Not specified" }}</p>
                                <p><strong>Expected Date of Delivery:</strong> {{ record.edd|date:"M d, Y"|default:"Not specified" }}</p>
                                <p><strong>Fundal Height:</strong> {{ record.fundal_height|default:"Not measured" }} cm</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Fetal Heartbeat:</strong> 
                                    {% if record.fetal_heartbeat %}
                                        <span class="badge bg-success">Detected</span>
                                    {% else %}
                                        <span class="badge bg-danger">Not detected</span>
                                    {% endif %}
                                </p>
                                <p><strong>Fetal Position:</strong> {{ record.fetal_position|default:"Not assessed" }}</p>
                                <p><strong>Blood Pressure:</strong> {{ record.blood_pressure|default:"Not measured" }}</p>
                                <p><strong>Urine Protein:</strong> {{ record.urine_protein|default:"Not tested" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5>Additional Information</h5>
                        <p><strong>Notes:</strong> {{ record.notes|default:"No notes" }}</p>
                        {% if record.authorization_code %}
                            <p><strong>Authorization Code:</strong> {{ record.authorization_code }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <p><strong>Created At:</strong> {{ record.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Last Updated:</strong> {{ record.updated_at|date:"M d, Y H:i" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}