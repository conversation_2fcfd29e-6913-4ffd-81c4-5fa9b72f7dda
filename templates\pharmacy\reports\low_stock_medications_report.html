{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">{{ title }}</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">Low Stock Medications</h6>
        </div>
        <div class="card-body">
            {% if low_stock_items %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Medication</th>
                            <th>Dispensary</th>
                            <th>Current Stock</th>
                            <th>Reorder Level</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in low_stock_items %}
                        <tr>
                            <td>{{ item.medication.name }} ({{ item.medication.strength }})</td>
                            <td>{{ item.active_store.dispensary.name }}</td>
                            <td>{{ item.stock_quantity }}</td>
                            <td>{{ item.reorder_level }}</td>
                            <td>
                                {% if item.stock_quantity == 0 %}
                                <span class="badge badge-danger">Out of Stock</span>
                                {% else %}
                                <span class="badge badge-warning">Low Stock</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <p>No low stock medications found.</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}