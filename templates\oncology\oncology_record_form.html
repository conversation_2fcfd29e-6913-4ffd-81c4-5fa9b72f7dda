{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - Hospital Management System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ title }}</h4>
                <a href="{% url 'oncology:oncology_records_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.patient|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.doctor|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.visit_date|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.authorization_code|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.cancer_type|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.stage|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.tumor_size|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.metastasis|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.diagnosis|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.treatment_plan|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.treatment_protocol|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.chemotherapy_cycle|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.radiation_dose|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.surgery_details|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.biopsy_results|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.oncology_marker|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.follow_up_required|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.follow_up_date|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            {{ form.notes|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Record
                        </button>
                        <a href="{% url 'oncology:oncology_records_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}