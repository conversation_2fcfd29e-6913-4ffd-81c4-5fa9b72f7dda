{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .conditional-field {
        display: none;
    }
    .required-field {
        border-left: 3px solid #e74a3b;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-box-open text-primary"></i> {{ page_title }}
        </h1>
        <a href="{% if pack %}{% url 'pharmacy:medical_pack_detail' pack.id %}{% else %}{% url 'pharmacy:medical_pack_list' %}{% endif %}" 
           class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        {% if pack %}Edit Medical Pack{% else %}Create New Medical Pack{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="post" id="packForm">
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h5 class="mb-3"><i class="fas fa-info-circle text-primary"></i> Basic Information</h5>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="{{ form.name.id_for_label }}" class="form-label">
                                            Pack Name <span class="text-danger">*</span>
                                        </label>
                                        {{ form.name }}
                                        {% if form.name.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.name.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="{{ form.is_active.id_for_label }}" class="form-label">Status</label>
                                        <div class="form-check">
                                            {{ form.is_active }}
                                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                                Active
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.description.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Pack Type and Specifications -->
                        <div class="form-section">
                            <h5 class="mb-3"><i class="fas fa-tags text-primary"></i> Pack Classification</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.pack_type.id_for_label }}" class="form-label">
                                            Pack Type <span class="text-danger">*</span>
                                        </label>
                                        {{ form.pack_type }}
                                        {% if form.pack_type.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.pack_type.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.risk_level.id_for_label }}" class="form-label">
                                            Risk Level <span class="text-danger">*</span>
                                        </label>
                                        {{ form.risk_level }}
                                        {% if form.risk_level.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.risk_level.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3 conditional-field" id="surgery-type-field">
                                        <label for="{{ form.surgery_type.id_for_label }}" class="form-label">
                                            Surgery Type <span class="text-danger">*</span>
                                        </label>
                                        {{ form.surgery_type }}
                                        {% if form.surgery_type.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.surgery_type.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3 conditional-field" id="labor-type-field">
                                        <label for="{{ form.labor_type.id_for_label }}" class="form-label">
                                            Labor Type <span class="text-danger">*</span>
                                        </label>
                                        {{ form.labor_type }}
                                        {% if form.labor_type.errors %}
                                            <div class="invalid-feedback d-block">
                                                {{ form.labor_type.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Settings -->
                        <div class="form-section">
                            <h5 class="mb-3"><i class="fas fa-shield-alt text-primary"></i> Approval Settings</h5>
                            
                            <div class="form-check">
                                {{ form.requires_approval }}
                                <label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">
                                    Requires approval before ordering
                                </label>
                                <div class="form-text">
                                    Check this if orders for this pack need to be approved before processing.
                                </div>
                            </div>
                        </div>

                        <!-- Form Errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{% if pack %}{% url 'pharmacy:medical_pack_detail' pack.id %}{% else %}{% url 'pharmacy:medical_pack_list' %}{% endif %}" 
                               class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                {% if pack %}Update Pack{% else %}Create Pack{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle pack type changes
    function handlePackTypeChange() {
        const packType = $('#id_pack_type').val();
        
        // Hide all conditional fields
        $('.conditional-field').hide();
        
        // Show relevant fields based on pack type
        if (packType === 'surgery') {
            $('#surgery-type-field').show();
            $('#id_surgery_type').prop('required', true);
            $('#id_labor_type').prop('required', false);
        } else if (packType === 'labor') {
            $('#labor-type-field').show();
            $('#id_labor_type').prop('required', true);
            $('#id_surgery_type').prop('required', false);
        } else {
            $('#id_surgery_type').prop('required', false);
            $('#id_labor_type').prop('required', false);
        }
    }

    // Initial call and event handler
    handlePackTypeChange();
    $('#id_pack_type').change(handlePackTypeChange);

    // Form validation
    $('#packForm').on('submit', function(e) {
        const packType = $('#id_pack_type').val();
        
        if (packType === 'surgery' && !$('#id_surgery_type').val()) {
            e.preventDefault();
            alert('Surgery type is required for surgery packs.');
            return false;
        }
        
        if (packType === 'labor' && !$('#id_labor_type').val()) {
            e.preventDefault();
            alert('Labor type is required for labor packs.');
            return false;
        }
    });

    // Auto-set requires_approval based on risk level
    $('#id_risk_level').change(function() {
        const riskLevel = $(this).val();
        if (riskLevel === 'high' || riskLevel === 'critical') {
            $('#id_requires_approval').prop('checked', true);
        }
    });

    // Add visual indicators for required fields
    $('input[required], select[required], textarea[required]').addClass('required-field');
});
</script>
{% endblock %}