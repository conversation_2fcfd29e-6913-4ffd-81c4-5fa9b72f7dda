# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='OncologyRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('cancer_type', models.CharField(blank=True, max_length=100, null=True)),
                ('stage', models.CharField(blank=True, max_length=20, null=True)),
                ('tumor_size', models.DecimalField(blank=True, decimal_places=2, help_text='Tumor size in cm', max_digits=5, null=True)),
                ('metastasis', models.BooleanField(default=False)),
                ('treatment_protocol', models.TextField(blank=True, null=True)),
                ('chemotherapy_cycle', models.IntegerField(blank=True, null=True)),
                ('radiation_dose', models.DecimalField(blank=True, decimal_places=2, help_text='Radiation dose in Gy', max_digits=6, null=True)),
                ('surgery_details', models.TextField(blank=True, null=True)),
                ('biopsy_results', models.TextField(blank=True, null=True)),
                ('oncology_marker', models.TextField(blank=True, null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='oncology_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Oncology Record',
                'verbose_name_plural': 'Oncology Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
