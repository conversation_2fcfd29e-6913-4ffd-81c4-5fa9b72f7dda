{% load static %}
{% load custom_filters %}

<div class="revenue-summary-widget">
    {% if not error %}
    <!-- Widget Header -->
    <div class="widget-header d-flex justify-content-between align-items-center mb-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-chart-pie"></i> Revenue Summary
        </h6>
        <small class="text-muted">{{ date_range }}</small>
    </div>

    <!-- Revenue Cards -->
    <div class="row">
        <!-- Total Revenue -->
        <div class="col-12 mb-3">
            <div class="card border-left-primary">
                <div class="card-body py-2">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ₦{{ widget_data.total_revenue|floatformat:2|intcomma }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clinical Revenue -->
        <div class="col-6 mb-2">
            <div class="card border-left-success">
                <div class="card-body py-2">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        Clinical
                    </div>
                    <div class="text-sm font-weight-bold text-gray-800">
                        ₦{{ widget_data.clinical_revenue|floatformat:2|intcomma }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Revenue -->
        <div class="col-6 mb-2">
            <div class="card border-left-info">
                <div class="card-body py-2">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        Support
                    </div>
                    <div class="text-sm font-weight-bold text-gray-800">
                        ₦{{ widget_data.support_revenue|floatformat:2|intcomma }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Specialty Revenue -->
        <div class="col-6 mb-2">
            <div class="card border-left-warning">
                <div class="card-body py-2">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        Specialty
                    </div>
                    <div class="text-sm font-weight-bold text-gray-800">
                        ₦{{ widget_data.specialty_revenue|floatformat:2|intcomma }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Growth Indicator -->
        <div class="col-6 mb-2">
            <div class="card border-left-secondary">
                <div class="card-body py-2">
                    <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                        Growth
                    </div>
                    <div class="text-sm font-weight-bold {% if widget_data.growth_indicator >= 0 %}text-success{% else %}text-danger{% endif %}">
                        {% if widget_data.growth_indicator >= 0 %}+{% endif %}{{ widget_data.growth_indicator }}%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Department -->
    <div class="mt-3">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-xs font-weight-bold text-uppercase">Top Department:</span>
            <span class="text-sm font-weight-bold">{{ widget_data.top_department.name }}</span>
        </div>
        <div class="progress progress-sm mt-1">
            <div class="progress-bar bg-primary" role="progressbar" style="width: 75%"></div>
        </div>
        <div class="text-right">
            <span class="text-xs text-muted">₦{{ widget_data.top_department.revenue|floatformat:2|intcomma }}</span>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-3 text-center">
        <a href="{% url 'core:revenue_point_dashboard' %}" class="btn btn-primary btn-sm">
            <i class="fas fa-chart-line"></i> Detailed Analysis
        </a>
    </div>

    {% else %}
    <!-- Error State -->
    <div class="alert alert-warning alert-sm">
        <i class="fas fa-exclamation-triangle"></i>
        <small>Unable to load revenue data: {{ error }}</small>
    </div>
    {% endif %}
</div>

<style>
.revenue-summary-widget {
    background: #fff;
    border-radius: 0.35rem;
    padding: 1rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.widget-header {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 0.5rem;
}

.progress-sm {
    height: 0.5rem;
}

.text-sm {
    font-size: 0.875rem;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-secondary {
    border-left: 0.25rem solid #858796 !important;
}
</style>