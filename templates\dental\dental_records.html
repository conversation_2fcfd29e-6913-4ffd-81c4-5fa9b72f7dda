{% extends 'base.html' %}

{% block title %}Dental Records{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Dental Records</h4>
                <a href="{% url 'dental:create_dental_record' %}" class="btn btn-light">
                    <i class="fas fa-plus"></i> Create New Record
                </a>
            </div>
            <div class="card-body">
                <!-- Search Form -->
                <form method="GET" class="mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="{{ search_form.search.id_for_label }}" class="form-label">Search</label>
                            {{ search_form.search }}
                        </div>
                        <div class="col-md-3">
                            <label for="{{ search_form.date_from.id_for_label }}" class="form-label">From Date</label>
                            {{ search_form.date_from }}
                        </div>
                        <div class="col-md-3">
                            <label for="{{ search_form.date_to.id_for_label }}" class="form-label">To Date</label>
                            {{ search_form.date_to }}
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div>
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'dental:dental_records' %}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> Reset
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h5 class="card-title">{{ total_records }}</h5>
                                <p class="card-text">Total Records</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Records Table -->
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Patient</th>
                                <th>Patient ID</th>
                                <th>Created Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'dental:dental_record_detail' record.id %}">
                                        {{ record.patient.get_full_name }}
                                    </a>
                                </td>
                                <td>{{ record.patient.patient_id }}</td>
                                <td>{{ record.created_at|date:"M d, Y H:i" }}</td>
                                <td>
                                    <a href="{% url 'dental:dental_record_detail' record.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="{% url 'dental:edit_dental_record' record.id %}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'dental:create_prescription_for_dental' record.id %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-prescription"></i> Prescribe
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">No dental records found.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No dental records available.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}