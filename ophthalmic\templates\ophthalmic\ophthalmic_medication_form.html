{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="POST">
                {% csrf_token %}
                <div class="form-group">
                    <label for="{{ form.medication_name.id_for_label }}">Medication Name *</label>
                    {{ form.medication_name|add_class:"form-control" }}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.dosage.id_for_label }}">Dosage *</label>
                            {{ form.dosage|add_class:"form-control" }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.frequency.id_for_label }}">Frequency *</label>
                            {{ form.frequency|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.duration.id_for_label }}">Duration</label>
                            {{ form.duration|add_class:"form-control" }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.instructions.id_for_label }}">Instructions</label>
                    {{ form.instructions|add_class:"form-control" }}
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Medication
                </button>
                <a href="{% url 'ophthalmic:ophthalmic_record_detail' record.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}