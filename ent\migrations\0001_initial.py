# Generated by Django 5.2 on 2025-08-16 16:21

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('doctors', '0001_initial'),
        ('patients', '0006_add_new_transaction_types'),
    ]

    operations = [
        migrations.CreateModel(
            name='EntRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('visit_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('external_ear_right', models.TextField(blank=True, help_text='External ear examination - Right', null=True)),
                ('external_ear_left', models.TextField(blank=True, help_text='External ear examination - Left', null=True)),
                ('ear_canal_right', models.TextField(blank=True, help_text='Ear canal examination - Right', null=True)),
                ('ear_canal_left', models.TextField(blank=True, help_text='Ear canal examination - Left', null=True)),
                ('tympanic_membrane_right', models.TextField(blank=True, help_text='Tympanic membrane examination - Right', null=True)),
                ('tympanic_membrane_left', models.TextField(blank=True, help_text='Tympanic membrane examination - Left', null=True)),
                ('nose_examination', models.TextField(blank=True, help_text='Nasal examination', null=True)),
                ('throat_examination', models.TextField(blank=True, help_text='Throat examination', null=True)),
                ('neck_examination', models.TextField(blank=True, help_text='Neck examination', null=True)),
                ('audio_test_right', models.TextField(blank=True, help_text='Audio test results - Right', null=True)),
                ('audio_test_left', models.TextField(blank=True, help_text='Audio test results - Left', null=True)),
                ('diagnosis', models.TextField(blank=True, null=True)),
                ('treatment_plan', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='doctors.doctor')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ent_records', to='patients.patient')),
            ],
            options={
                'verbose_name': 'Ent Record',
                'verbose_name_plural': 'Ent Records',
                'ordering': ['-visit_date'],
            },
        ),
    ]
